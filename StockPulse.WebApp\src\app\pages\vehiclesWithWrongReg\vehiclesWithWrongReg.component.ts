import { Component, OnInit } from '@angular/core';
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { Subscription } from 'rxjs';
import { ToastService } from 'src/app/services/newToast.service';
import { VehiclesWithWrongRegService } from './vehiclesWithWrongReg.service';

@Component({
    selector: 'app-vehiclesWithWrongReg',
    templateUrl: './vehiclesWithWrongReg.component.html',
    styleUrls: ['./vehiclesWithWrongReg.component.scss'],
    standalone: false
})
export class VehiclesWithWrongRegComponent implements OnInit {
  firstLoad: boolean = true;
  modalRef: any;
  pageRefreshSubscription: Subscription;

  constructor(
    public service: VehiclesWithWrongRegService,
    public constants: ConstantsService
  ) { }

  ngOnInit() {
    this.initParams();
    this.getData()

    this.pageRefreshSubscription = this.constants.refreshPage.subscribe((res) => {
      if (res) {
        this.initParams();
        this.getData()
      }
    })
  }

  ngAfterViewChecked() {

    if(this.firstLoad) {
      this.loadDefault();
    }

  }

  initParams() {
    this.service.initVehiclesWithWrongReg();

    this.pageRefreshSubscription = this.service.constants.refreshPage.subscribe((res) => {
      if(res){this.refreshData();}
    })

  }

  ngOnDestroy() {
    if (this.pageRefreshSubscription) this.pageRefreshSubscription.unsubscribe();
  }


  getData(){
    this.service.api.get('scans',`GetScanRegDifference`,[{key:'stockcheckId',value:this.service.selections.stockCheck.id}]).subscribe(data => {
      this.service.vehicles=data;

      this.service.vehicles.forEach(item => {
        this.service.constants.addImageThumbnailStringsToScanRegDifference(item);
      });
      
      this.service.toastService.destroyToast();
    });
  }

  refreshData(){
    this.service.toastService.loadingToast();

    this.getData();

    this.service.toastService.successToast('Mismatches refreshed');
  }



  showMismatchOption(mismatchOption:string){
    
    this.service.mismatchOption = mismatchOption;
    if(mismatchOption == 'Reg differences')this.service.scanRegDifferences = this.service.vehicles.filter(x=>!x.regMatches)
    if(mismatchOption == 'VIN differences')this.service.scanRegDifferences = this.service.vehicles.filter(x=>x.regMatches)
    if(mismatchOption == 'All differences')this.service.scanRegDifferences = this.service.vehicles
    this.firstLoad = false
    
  }

  loadDefault() {
    this.service.scanRegDifferences = this.service.vehicles.filter(x=>!x.regMatches)
  }

  goToVehicleScan(stockCheckItem:any){
    this.service.constants.vehicleModal.loadItemAndOpenModal(false, stockCheckItem.scanId,[]);
  }

}
