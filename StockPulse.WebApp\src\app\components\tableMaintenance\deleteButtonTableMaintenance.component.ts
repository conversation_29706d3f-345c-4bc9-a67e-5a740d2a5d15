import { Component, ViewChild } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";
import { TableMaintenanceService } from "./tableMaintenance.service";
import { ConstantsService } from "src/app/services/constants.service";
import { IconService } from "src/app/services/icon.service";
import { SelectionsService } from "src/app/services/selections.service";



// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'deleteButtonTableMaintenance-cell',
    template: `
   
        <button *ngIf="params.data" class="btn btn-danger" (click)="delete()">
            <fa-icon [icon]="icon.faTrash"> Delete </fa-icon>
            <i class="fas fa-trash"></i>
            </button>    
    `,
    styles: [
        `
        button{
            position: absolute;
            width: 70%;
            height: 100%;
            top: 0;
            left: 15%;
            margin: 0;
            display: flex;
            justify-content: center;
            border-radius: 0.25rem;
            align-items: center;
        }

        .btn{opacity:0.2}
        .btn:hover{opacity:1}
       
      `
    ],
    standalone: false
})
export class DeleteButtonTableMaintenanceComponent implements ICellRendererAngularComp {
   
params:any;


    constructor(
        
        public constants: ConstantsService,
        public service: TableMaintenanceService,
        public icon: IconService,
        public selections: SelectionsService,

    ) { }


    agInit(params: any): void {

    this.params = params

    }
    refresh(): boolean {
        return false;
    }


    delete(){

        this.constants.confirmModal.confirmModalHeader = 'Really delete this row?';
            this.constants.confirmModal.isDestructive = true;
            let mySubscription = this.selections.confirmModalEmitter.subscribe(res => {
                if (res) { this.deleteOK(); }
                mySubscription.unsubscribe();
            })

            this.constants.confirmModal.showModal();
    }

    deleteOK(){
        console.log('call api to delete the row', this.params.data);
        
        
        this.service.deleteRow(this.service.selectedTableName, this.params.data.Id);
        // this.getdata.deleteUser('api/Users','UserAndLogin',this.params.data.Id).subscribe(res => {
        //     this.constants.toastSuccess('Deleted user')
        //     //console.log(this.params);
        //     //to do let matchingIndex = this.params.context.
        //     let parentRowData = this.params.context.componentParent.selections.userSetup.rowData;

        //     let itemIndex = parentRowData.findIndex(x=>x.Id === this.params.data.Id);
        //     parentRowData.splice(itemIndex,1);
        //     this.params.api.setRowData(parentRowData)
        //     this.params.api.refreshcells();

        // },e=>{
        //     if (e !== 'Bad Request') {
        //         this.constants.toastDanger('Failed to delete user');
        //     }
        // })
        
    }


     


}


