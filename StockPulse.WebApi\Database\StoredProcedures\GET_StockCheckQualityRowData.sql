
CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckQualityRowData]
(
    @UserId INT = NULL,
    @IsActive BIT,
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)  
AS  
BEGIN

  SET NOCOUNT ON;    

  -- Get the DealerGroupId from the user
  DECLARE @DealerGroupId INT = (SELECT TOP 1 DealerGroupId FROM Users WHERE Id = @UserId);

  -- Pre-aggregate scan and stockitem info by stockcheck
  WITH ScanTotCounts AS (
      SELECT
          sca.StockCheckId,
          COUNT(*) as ScanCount
      FROM Scans sca
      INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional=0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
      INNER JOIN Sites site ON site.Id = sc.SiteId
      INNER JOIN Divisions div ON div.Id = site.DivisionId AND div.DealerGroupId = @DealerGroupId
      GROUP BY StockCheckId
  ),
   StkTotCounts AS (
      SELECT
          si.StockCheckId,
          COUNT(*) as StockItemsCount
      FROM StockItems si
      INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional=0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
      INNER JOIN Sites site ON site.Id = sc.SiteId
      INNER JOIN Divisions div ON div.Id = site.DivisionId AND div.DealerGroupId = @DealerGroupId
      GROUP BY StockCheckId
  ),
  ScanEditCounts AS (
      SELECT
          sca.StockCheckId,
          SUM(CASE WHEN sca.IsRegEditedOnDevice = 1 THEN 1 ELSE 0 END) AS RegsEditedOnDevice,
          SUM(CASE WHEN sca.IsRegEditedOnWeb = 1 THEN 1 ELSE 0 END) AS RegsEditedOnWeb,
          SUM(CASE WHEN sca.IsVinEditedOnDevice = 1 THEN 1 ELSE 0 END) AS VinsEditedOnDevice,
          SUM(CASE WHEN sca.IsVinEditedOnWeb = 1 THEN 1 ELSE 0 END) AS VinsEditedOnWeb
      FROM Scans sca 
      INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional=0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
      INNER JOIN Sites site ON site.Id = sc.SiteId
      INNER JOIN Divisions div ON div.Id = site.DivisionId AND div.DealerGroupId = @DealerGroupId
      GROUP BY StockCheckId
  ),

    StockItemCounts AS (
        SELECT 
            si.StockCheckId,
            CASE
               WHEN si.ScanId IS NOT NULL AND sca.StockCheckId = sc.Id THEN 'MatchedToScan'
               WHEN si.IsDuplicate = 1 THEN 'Duplicate'
               WHEN si.ReconcilingItemId IS NOT NULL OR si.StockCheckId != sca.StockCheckId THEN 'AutoResolved'
               WHEN mr.Id IS NOT NULL AND mr.IsResolved = 1 THEN 'ManuallyResolved'
               ELSE 'Unresolved'
            END as Status,
            COUNT(*) as ItemCount
        FROM StockItems si
        INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional=0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions div ON div.Id = s.DivisionId AND div.DealerGroupId = @DealerGroupId
        LEFT JOIN MissingResolutions mr ON mr.Id = si.MissingResolutionId
		LEFT JOIN Scans sca ON sca.Id = si.ScanId
        GROUP BY 
        si.StockCheckId,
        CASE
               WHEN si.ScanId IS NOT NULL AND sca.StockCheckId = sc.Id THEN 'MatchedToScan'
               WHEN si.IsDuplicate = 1 THEN 'Duplicate'
               WHEN si.ReconcilingItemId IS NOT NULL OR si.StockCheckId != sca.StockCheckId THEN 'AutoResolved'
               WHEN mr.Id IS NOT NULL AND mr.IsResolved = 1 THEN 'ManuallyResolved'
               ELSE 'Unresolved'
            END
    ),

     ScanCounts AS (
        SELECT 
            sca.StockCheckId,
            CASE
               WHEN sca.StockItemId IS NOT NULL AND sca.StockCheckId = si.StockCheckId THEN 'MatchedToStockItem'
               WHEN sca.IsDuplicate = 1 THEN 'Duplicate'
               WHEN sca.ReconcilingItemId IS NOT NULL OR sca.StockCheckId != si.StockCheckId THEN 'AutoResolved'
               WHEN ur.Id IS NOT NULL AND ur.IsResolved = 1 THEN 'ManuallyResolved'
               ELSE 'Unresolved'
            END as Status,
            COUNT(*) as ItemCount
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional=0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions div ON div.Id = s.DivisionId AND div.DealerGroupId = @DealerGroupId
        LEFT JOIN UnknownResolutions ur ON ur.Id = sca.UnknownResolutionId
        LEFT JOIN StockItems si ON si.Id = sca.StockItemId
        GROUP BY 
        sca.StockCheckId,
         CASE
               WHEN sca.StockItemId IS NOT NULL AND sca.StockCheckId = si.StockCheckId THEN 'MatchedToStockItem'
               WHEN sca.IsDuplicate = 1 THEN 'Duplicate'
               WHEN sca.ReconcilingItemId IS NOT NULL OR sca.StockCheckId != si.StockCheckId THEN 'AutoResolved'
               WHEN ur.Id IS NOT NULL AND ur.IsResolved = 1 THEN 'ManuallyResolved'
               ELSE 'Unresolved'
            END
    )

  SELECT
      sc.Id AS StockCheckId,
      s.Description AS Site,
      d.Description AS Division,
	  sc.Date AS StockCheckDate,
      st.Description AS Status,
      cu.Name AS CompletedBy,
      sc.LastUpdated,

      COALESCE(scaa.ScanCount,0) as Scans,
      COALESCE(stki.StockItemsCount,0) as StockItems,

      COALESCE(scanEdits.RegsEditedOnDevice,0) + COALESCE(scanEdits.RegsEditedOnWeb,0)  as RegsEdited,
      COALESCE(scanEdits.VinsEditedOnDevice,0) + COALESCE(scanEdits.VinsEditedOnWeb,0)  as VinsEdited,

      COALESCE(scanDuplicate.ItemCount,0) as DuplicateScans,
      COALESCE(scanAutoResolved.ItemCount,0) + COALESCE(scanDuplicate.ItemCount,0) as UnknownsAutoResolved,
      COALESCE(scanManuallyResolved.ItemCount,0) as UnknownsManuallyResolved,
      COALESCE(scanUnresolved.ItemCount,0) as UnknownsUnresolved,

      COALESCE(stkMatchedToScan.ItemCount,0) as ScannedInStock,
      COALESCE(stkDuplicate.ItemCount,0) as DuplicateStockItems,
      COALESCE(stkAutoResolved.ItemCount,0) as MissingsAutoResolved,
      COALESCE(stkManuallyResolved.ItemCount,0) as MissingsManuallyResolved,
      COALESCE(stkUnresolved.ItemCount,0) as MissingsUnresolved

  FROM StockChecks sc
  INNER JOIN Sites s ON s.Id = sc.SiteId
  INNER JOIN Statuses st ON st.Id = sc.StatusId
  INNER JOIN Divisions d ON d.Id = s.DivisionId AND d.DealerGroupId = @DealerGroupId
  --INNER JOIN Scans sca on sca.StockCheckId = sc.Id

  LEFT JOIN Users cu ON cu.Id = sc.ApprovedByAccountantId

  --Scans and StockItems
  LEFT JOIN  ScanTotCounts scaa on scaa.StockCheckId = sc.Id
  LEFT JOIN StkTotCounts stki on stki.StockCheckId = sc.Id
  
  --ScanEdits
  LEFT JOIN ScanEditCounts scanEdits ON scanEdits.StockCheckId = sc.Id
  
  --ScanCounts
  LEFT JOIN ScanCounts scanDuplicate ON scanDuplicate.StockCheckId = sc.Id AND scanDuplicate.Status = 'Duplicate'
  LEFT JOIN ScanCounts scanAutoResolved ON scanAutoResolved.StockCheckId = sc.Id AND scanAutoResolved.Status = 'AutoResolved'
  LEFT JOIN ScanCounts scanManuallyResolved ON scanManuallyResolved.StockCheckId = sc.Id AND scanManuallyResolved.Status = 'ManuallyResolved'
  LEFT JOIN ScanCounts scanUnresolved ON scanUnresolved.StockCheckId = sc.Id AND scanUnresolved.Status = 'Unresolved'

  --StockItemCount
  LEFT JOIN StockItemCounts stkMatchedToScan ON stkMatchedToScan.StockCheckId = sc.Id AND stkMatchedToScan.Status = 'MatchedToScan'
  LEFT JOIN StockItemCounts stkDuplicate ON stkDuplicate.StockCheckId = sc.Id AND stkDuplicate.Status = 'Duplicate'
  LEFT JOIN StockItemCounts stkAutoResolved ON stkAutoResolved.StockCheckId = sc.Id AND stkAutoResolved.Status = 'AutoResolved'
  LEFT JOIN StockItemCounts stkManuallyResolved ON stkManuallyResolved.StockCheckId = sc.Id AND stkManuallyResolved.Status = 'ManuallyResolved'
  LEFT JOIN StockItemCounts stkUnresolved ON stkUnresolved.StockCheckId = sc.Id AND stkUnresolved.Status = 'Unresolved'
  WHERE
      (@IsActive IS NULL OR SC.IsActive = @IsActive) AND
	  sc.IsRegional = 0 AND
	  sc.IsTotal = 0 AND
      (@FromDate IS NULL OR sc.Date >= @FromDate) AND
      (@ToDate IS NULL OR sc.Date <= @ToDate)
  

  ORDER BY
	  sc.[Date] desc, 
     s.[Description]

END
GO
