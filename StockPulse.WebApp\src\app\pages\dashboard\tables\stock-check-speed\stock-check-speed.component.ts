import { Component, HostListener, Input } from "@angular/core";
import {
  Col<PERSON><PERSON>,
  Column,
  ColumnApi,
  GridApi,
  GridOptions,
  GridReadyEvent,
} from "ag-grid-community";
import { Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { ToastService } from "src/app/services/newToast.service";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { DashboardService } from "../../dashboard.service";
import { LogoService } from "src/app/services/logo.service";
import { NavigationStart, Router } from "@angular/router";

@Component({
  selector: "app-stock-check-speed",
  templateUrl: "./stock-check-speed.component.html",
  styleUrls: ["./stock-check-speed.component.scss"],
})
export class StockCheckSpeedComponent {
  @HostListener("window:resize", [])
  private onresize(event) {
    if (this.gridApi) {
      this.adjustColumnSizing();
    }
  }

  public gridOptions: GridOptions;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;
  private sidenavToggledSubscription: Subscription;
  private routerSubscription: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    public toastService: ToastService,
    public excelExportService: ExcelExportService,
    public dashboardService: DashboardService,
    private logoService: LogoService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    this.sidenavToggledSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  ngOnInit(): void {
    this.initialiseGrid();

    this.sidenavToggledSubscription =
      this.constantsService.sidenavToggledEmitter.subscribe(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });

    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dashboardService.stockCheckSpeedColumnState =
          this.gridColumnApi.getColumnState();
        this.dashboardService.stockCheckSpeedfilterModel =
          this.gridApi.getFilterModel();
      }
    });
  }

  initialiseGrid(): void {
    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      headerHeight: 50,
      floatingFiltersHeight: 25,
      rowHeight: 25,
      context: { thisComponent: this },
      defaultColDef: {
        sortable: true,
        resizable: true,
        floatingFilter: true,
        autoHeight: true,
        autoHeaderHeight: true,
        wrapHeaderText: true,
      },
      columnTypes: {
        label: { filter: "agTextColumnFilter" },
        number: {
          filter: "agNumberColumnFilter",
          cellClass: "agAlignRight",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "number", 0)
              : "-",
        },
        date: {
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "date", 0)
              : "-",
        },
        dayMonthTime: {
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "dayMonthAndTime", 0)
              : null,
        },
      },
      columnDefs: [
        {
          headerName: "Id",
          field: "stockCheckId",
          type: "label",
          width: 60,
          pinned: "left",
        },
        {
          headerName: "Site",
          field: "site",
          type: "label",
          width: 150,
          pinned: "left",
        },
        {
          headerName: "Division",
          field: "division",
          type: "label",
          width: 80,
          pinned: "left",
        },
        {
          headerName: "Stock Check Date",
          field: "stockCheckDate",
          type: "date",
          width: 100,
          pinned: "left",
        },
        {
          headerName: "Status",
          valueGetter: (params) => {
            return this.constantsService.abbreviateStockCheckStatus(
              params.data.status
            );
          },
          type: "label",
          width: 120,
          pinned: "left",
        },
        {
          headerName: "Completed By",
          field: "completedBy",
          type: "label",
          width: 130,
          pinned: "left",
        },
        {
          headerName: "Approved By",
          field: "approvedBy",
          type: "label",
          width: 130,
          pinned: "left",
        },
        {
          headerName: "Stock Loaded",
          field: "stockLoaded",
          type: "dayMonthTime",
          width: 100,
        },
        {
          headerName: "Scans",
          field: "scans",
          type: "number",
          width: 60,
        },
        {
          headerName: "First Scan",
          field: "firstScan",
          type: "dayMonthTime",
          width: 100,
        },
        {
          headerName: "Last Scan",
          field: "lastScan",
          type: "dayMonthTime",
          width: 100,
        },
        {
          headerName: "Scan Duration (hrs)",
          field: "scanDurationHours",
          type: "number",
          width: 80,
        },
        {
          headerName: "Reconciliation Completed",
          field: "reconciliationCompleted",
          type: "dayMonthTime",
          width: 115,
        },
        {
          headerName: "Time to Complete (hrs)",
          field: "timeToCompleteHours",
          type: "number",
          width: 80,
        },
        {
          headerName: "Reconciliation Approved",
          field: "reconciliationApproved",
          type: "dayMonthTime",
          width: 115,
        },
        {
          headerName: "Time to Approve (hrs)",
          field: "timeToApproveHours",
          type: "number",
          width: 80,
        },
        {
          headerName: "Total Duration (hrs)",
          field: "totalDurationHours",
          type: "number",
          width: 80,
        },
        {
          headerName: "Average Time to Resolve Unknowns (hrs)",
          field: "timeToResolveUnknownsHours",
          type: "number",
          width: 115,
        },
        {
          headerName: "Average Time to Resolve Missings (hrs)",
          field: "timeToResolveMissingsHours",
          type: "number",
          width: 115,
        },
      ],
      onGridReady: (event) => this.onGridReady(event),
      onColumnGroupOpened: (event) => {
        this.gridApi.sizeColumnsToFit();
        this.dashboardService.stockCheckSpeedColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onColumnMoved: (event) => {
        this.dashboardService.stockCheckSpeedColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onFilterChanged: (event) => {
        this.dashboardService.stockCheckSpeedfilterModel =
          this.gridApi.getFilterModel();
      },
      onSortChanged: (event) => {
        this.dashboardService.stockCheckSpeedColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onRowDoubleClicked: (event) => {
        this.dashboardService.loadStockCheckAndGoToReconcile(
          event.data.stockCheckId
        );
      },
    };
  }

  onGridReady(event: GridReadyEvent): void {
    this.gridApi = event.api;
    this.gridColumnApi = event.columnApi;

    if (this.dashboardService.stockCheckSpeedColumnState) {
      this.gridColumnApi.applyColumnState({
        state: this.dashboardService.stockCheckSpeedColumnState,
        applyOrder: true,
      });
    }
    if (this.dashboardService.stockCheckSpeedfilterModel) {
      this.gridApi.setFilterModel(
        this.dashboardService.stockCheckSpeedfilterModel
      );
    }

    this.adjustColumnSizing();
  }

  download(): void {
    this.toastService.loadingToast("Generating Excel file...");

    const filteredRows = [];
    const columns: Column[] = this.gridColumnApi.getColumns();

    this.gridApi.forEachNodeAfterFilter((n) => {
      const columnsForExcel = {};

      columns.forEach((column) => {
        const columnId: string = column.getColId();
        columnsForExcel[columnId] = n.data[columnId];
      });

      columnsForExcel["stockLoaded"] = n.data.stockLoaded
        ? new Date(n.data.stockLoaded)
        : null;
      columnsForExcel["firstScan"] = n.data.firstScan
        ? new Date(n.data.firstScan)
        : null;
      columnsForExcel["lastScan"] = n.data.lastScan
        ? new Date(n.data.lastScan)
        : null;
      columnsForExcel["reconciliationCompleted"] = n.data
        .reconciliationCompleted
        ? new Date(n.data.reconciliationCompleted)
        : null;
      columnsForExcel["reconciliationApproved"] = n.data.reconciliationApproved
        ? new Date(n.data.reconciliationApproved)
        : null;

      filteredRows.push(columnsForExcel);
    });

    const colTypes: string[] = (this.gridApi.getColumnDefs() as ColDef[]).map(
      (x) => x.type
    ) as string[];

    let sheet: SheetToExtractOld = {
      tableData: filteredRows,
      tableName: `Stock Check Speed`,
      columnWidths: this.excelExportService.workoutColWidths(filteredRows),
      colTypes: colTypes,
    };

    this.dashboardService.downloadExcelFile(sheet);
  }

  convertTimeToExcelFormat(duration: string): number {
    const [h, m, s] = duration.split(":");
    const [sec, ms] = s.split(".");
    const hours = parseInt(h, 10);
    const minutes = parseInt(m, 10);
    const seconds = parseInt(sec, 10);
    const milliseconds = ms ? parseInt(ms.slice(0, 3), 10) : 0;
    const totalMilliseconds =
      hours * 3600000 + minutes * 60000 + seconds * 1000 + milliseconds;

    return totalMilliseconds / (24 * 60 * 60 * 1000);
  }

  get excelIconBackgroundImage() {
    return {
      "background-image": `url(${this.logoService.provideExcelLogo()})`,
    };
  }

  adjustColumnSizing(): void {
    if (!this.gridApi || !this.gridColumnApi) return;

    // Total width of all columns
    const allColumns = this.gridColumnApi.getAllDisplayedColumns();
    const totalColumnWidth = allColumns.reduce((sum, col) => {
      return (
        sum +
          this.gridColumnApi
            .getColumnState()
            .find((c) => c.colId === col.getColId())?.width || 0
      );
    }, 0);

    // Width of the grid container
    const gridElement = document.querySelector<HTMLElement>(
      "#stockCheckSpeedGrid"
    );
    const gridWidth = gridElement?.offsetWidth || 0;

    if (totalColumnWidth < gridWidth) {
      this.gridApi.sizeColumnsToFit();
    }
  }
}
