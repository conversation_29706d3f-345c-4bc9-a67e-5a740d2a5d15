import { <PERSON>mpo<PERSON>, <PERSON>ement<PERSON>ef, <PERSON><PERSON><PERSON>ter, HostListener, OnInit, ViewChild } from '@angular/core';
import { ColumnApi, GridApi, GridOptions } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { UserAndLogin } from "../../model/UserAndLogin";
import { SheetToExtractOld } from "../../model/SheetToExtractOld";
import { UserMaintenanceForExcel } from "../../model/UserMaintenanceForExcel";
import { ConstantsService } from '../../services/constants.service';
import { GridHelperService } from '../../services/gridHelper.service';
import { IconService } from '../../services/icon.service';
import { LogoService } from '../../services/logo.service';
import { SaveDataService } from '../../services/saveData.service';
import { SelectionsService } from '../../services/selections.service';
import { SiteVMWithSelected } from "../../model/SiteVMWithSelected";
import { CancelButtonUserSetupComponent } from './cancelButton';
import { DeleteButtonUserComponent } from './deleteButton';
import { RolePickerCellRendererComponent } from './rolePicker.component';
import { SaveButtonUserComponent } from './saveButton';
import { SitePickerCellRendererComponent } from './sitePickerCell.component';
import { SitesPickerCellRendererComponent } from './sitesPickerCell.component';

import * as excelJS from 'exceljs';
import * as fs from 'file-saver'; //angular compiler moans about this approach bloating the bundle.   using proper angular version doesn't seem to work.   removing this saves 0mb from the bundle.
import { ToastService } from 'src/app/services/newToast.service';
import { AppUserRole } from '../../model/AppUserRole';
import { HttpEventType } from '@angular/common/http';
import { RestoreButtonUserComponent } from './restoreButton';

@Component({
    selector: 'app-userSetup',
    templateUrl: './userSetup.component.html',
    styleUrls: ['./userSetup.component.scss'],
    standalone: false
})
export class UserSetupComponent implements OnInit {

  @ViewChild('tableContainer', { static: true }) tableContainer: ElementRef;

  @HostListener('window:resize', [])
  private onresize (event) {
    if (this.gridApi) this.sizeGrid();
  }

  mainTableGridOptions: GridOptions;
  gridApi: GridApi;
  gridColumnApi: ColumnApi;

  //userAndLoginRows: UserAndLogin[];
  showGrid: boolean
  subscription: Subscription;

  fileToUpload: File;
  fileReadyToUpload: boolean;
  progress: number;

  cancelPriceUpdateEmitter: EventEmitter<boolean>

  activeSites: SiteVMWithSelected[];
  sidenavToggledSubscription: Subscription;

  isAllUsers: boolean = true;
  isActiveUsers: boolean;
  isLockedUsers: boolean;
  isDeletedUsers: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public icon: IconService,
    public logo: LogoService,
    public cphPipe: CphPipe,
    public save: SaveDataService,
    public grid: GridHelperService,
    public apiAccess: ApiAccessService,
    public toastService: ToastService
  ) { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    this.selections.userSetup = {
      rowData : []
    }

    this.loadRoles();

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  loadRoles(){
    this.apiAccess.get('User','Roles').subscribe((roles: AppUserRole[])=>{
      this.constants.Roles = roles;
      this.loadSites();
    });
  }

  loadSites(){
    this.apiAccess.get('Sites','All').subscribe((sites: SiteVMWithSelected[])=>{

      let justSites: SiteVMWithSelected[] = sites.filter(x => x.description !== x.division && x.division !== 'Total').sort((a, b) => a.description.localeCompare(b.description));
      let divisions: SiteVMWithSelected[] = sites.filter(x => x.description === x.division && x.division !== 'Total').sort((a, b) => a.description.localeCompare(b.description));
      let total: SiteVMWithSelected[] = sites.filter(x => x.division === 'Total' ).sort((a, b) => a.description.localeCompare(b.description));
      let sitesAndDivisions: SiteVMWithSelected[] = justSites.concat(divisions);
      
      this.constants.SitesActive = sitesAndDivisions.concat(total).filter(x => x.isActive);
      this.getUserAndLoginsData();
    });
  }
  

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  initGridParams() {
    let baseHeight = window.innerWidth > 1550 ? 4 : 0
    this.mainTableGridOptions = {
      suppressColumnMoveAnimation: true,
      //frameworkComponents: { agColumnHeader: CustomHeaderComponent },
      getRowHeight: (params) => {
        let n = params.node.level
        if (n > 0) {
          return 20 + baseHeight
        } else {
          return 20 + baseHeight
        }

      },
      context: {
        componentParent: this
      },

      onGridReady: (params) => this.onGridReady(params),
      

      columnTypes: {
        "number": { cellClass: 'agAlignRight', cellRenderer: (params) => { if (!params.value) { return '-' } else return this.cphPipe.transform(params.value, 'number', 2, false) } },
        "label": { cellClass: 'agAlignCenter', filter: 'agTextColumnFilter', floatingFilter: true },

      },


      defaultColDef: { resizable: true },
      //getContextMenuItems: (params) => this.getContextMenuItems(params),
      pivotColumnGroupTotals: "after",
      //onRowEditingStopped:(params)=>this.(params),
      pivotMode: false,
      enableCellChangeFlash:true,
      onRowGroupOpened: (params) => this.onRowGroupOpened(params),
      suppressAggFuncInHeader: true,//hides the extra header row saying (Sum..)
      onColumnGroupOpened: () => this.sizeGrid(),
      rowClassRules:{'changed': (params)=>params.data.hasChanged, 'locked':(params)=>params.data.isLocked, 'deleted':(params)=>params.data.isDeleted},
      onCellEditingStopped: (event) => { this.onEditingStopped(event) },
      columnDefs: [
        { headerName: 'Code', field: 'code', colId: 'Code', width: 50, type: 'label', },
        { headerName: 'Name', field: 'name', colId: 'Name', width: 100, type: 'label', },
        { headerName: 'Employee Number', field: 'employeeNumber', colId: 'EmployeeNumber', width: 100, type: 'label', },
        //{ headerName: 'NameShort', field: 'nameShort', colId: 'NameShort', width: 100, type: 'label',editable: true, cellClass:'editable' },
        { headerName: 'Role', field: 'roleName', colId: 'RoleName', width: 200, type: 'label', minWidth: 200 },
        { headerName: 'Email', field: 'email', colId: 'email', width: 150, type: 'label', },
        {
          headerName: 'Sites', field: 'sites', colId: 'Sites', 
          filterValueGetter: (params) => this.sitesFilterGetter(params), 
          width: 200,
          valueGetter: (params) => this.generateSitesNames(params),
          // cellRenderer: SitesPickerCellRendererComponent, 
          type: 'label', minWidth: 200
        },
        {
          headerName: 'Home Site', field: 'siteCode', colId: 'Site', 
          filterValueGetter: (params) => params.node.data.siteCode ? this.constants.Sites.find(x => x.id == params.node.data.siteCode)?.description : '',
          width: 200, 
          valueGetter: (params) => this.generateSiteName(params),
          // cellRenderer: SitePickerCellRendererComponent, 
          type: 'label', minWidth: 200
        },

        {
          headerName: "State",
          valueGetter: (params) => {
            if (params.data.isDeleted) { return "Deleted"; }
            else if (params.data.isLocked) { return "Locked"; }
            else { return "Active"; }
          },
          width: 100,
          minWidth: 100,
          type: "label"
        },

        // { headerName: '', colId: 'Cancel', cellRenderer: CancelButtonUserSetupComponent, width: 30, minWidth: 50 },
        // { headerName: '', colId: 'Save', cellRenderer: SaveButtonUserComponent, width: 30, minWidth: 50 },
        { headerName: '', colId: 'Delete', cellRenderer: DeleteButtonUserComponent, width: 30, minWidth: 50, hide: !this.constants.userRolePermissions.canMakeUserChanges},
        { headerName: '', colId: 'Restore', cellRenderer: RestoreButtonUserComponent, width: 30, minWidth: 50, hide: !this.constants.userRolePermissions.canMakeUserChanges }
      ],
      onRowDoubleClicked: (params) => this.addUser(params)
    }



  }

  generateSitesNames(params) {
    if (!params.data.sites) { return; }
    const siteIds: string[] = params.data.sites.split(',');

    if (siteIds.length > 3) {
      return `${siteIds.length} sites`;
    } else {
      let siteNames: string = '';

      this.constants.Sites.forEach(site => {
        if (siteIds.includes(site.id.toString())) { siteNames += `${site.description},` }
      })

      return siteNames.slice(0, -1);
    }
  }

  generateSiteName(params) {
    const siteId: number = parseInt(params.data.siteCode);
    return this.constants.Sites.find(site => site.id === siteId)?.description;
  }


  sitesFilterGetter(params) {
    if (!params.data.sites) { return; }
    let returnString: string = '';
    let siteIds = params.data.sites.split(',').map(x => parseInt(x));
    siteIds.forEach(id => {
      let site = this.constants.Sites.find(x => x.id == id)
      returnString += site ? site.description : '';
    })
    return returnString;
  }


  onEditingStopped(params) {
    if (!params.valueChanged) return;
    let colId = params.colDef.colId
    let data = params.node.data;
    data.newValues[colId] = params.newValue
    data.hasChanged = true;
    params.node.setData(data);
    this.sizeGrid();
  }


  getUserAndLoginsData() {

    this.apiAccess.get('User','UserAndLogin').subscribe((res: UserAndLogin[])=>{
    //console.table(res);
      //got results
      this.selections.userSetup.rowData = res;
      this.selections.userSetup.rowData.map(x => {
        x.newValues = {
          name: null,
          nameShort: null,
          roleName: null,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
          sites: null,
          siteCode: null,
          email: null,
          employeeNumber: null
        };
        x.originalValues = {
          name: x.name,
          nameShort: x.nameShort,
          roleName: x.roleName,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
          sites: x.sites,
          siteCode: x.siteCode,
          email: this.constants.clone(x.email),
          employeeNumber: x.employeeNumber
        }
      })

      
      if (!this.mainTableGridOptions) {
        this.showGrid = true;
        this.initGridParams()
      } else {
        this.refreshGrid();
      }

    });

    
  }


  refreshGrid() {
    if (this.gridApi) this.gridApi.setRowData(this.selections.userSetup.rowData);
    if (this.gridApi) this.gridApi.refreshCells();
  }

  showAllUsers(){
    this.resetUserDisplayStatus();
    this.isAllUsers = true;

    if (this.gridApi) this.gridApi.setRowData(this.selections.userSetup.rowData);
    if (this.gridApi) this.gridApi.refreshCells();
  }

  showActiveUsers(){
    this.resetUserDisplayStatus();
    this.isActiveUsers = true;
    
    if (this.gridApi) this.gridApi.setRowData(this.selections.userSetup.rowData.filter(x => x.isDeleted == false && x.isLocked == false));
    if (this.gridApi) this.gridApi.refreshCells();
  }
  showLockedUsers(){
    this.resetUserDisplayStatus();
    this.isLockedUsers = true;
    
    if (this.gridApi) this.gridApi.setRowData(this.selections.userSetup.rowData.filter(x => x.isLocked == true));
    if (this.gridApi) this.gridApi.refreshCells();
  }

  showDeletedUsers(){
    this.resetUserDisplayStatus();
    this.isDeletedUsers = true;
    
    if (this.gridApi) this.gridApi.setRowData(this.selections.userSetup.rowData.filter(x => x.isDeleted == true));
    if (this.gridApi) this.gridApi.refreshCells();
  }

  resetUserDisplayStatus(){
    this.isActiveUsers = false;
    this.isLockedUsers = false;
    this.isDeletedUsers = false;
    this.isAllUsers = false;
  }

  showSelectedStatusUsers(){
    if (this.isActiveUsers) this.showActiveUsers();
    else if (this.isLockedUsers) this.showLockedUsers();
    else if (this.isDeletedUsers) this.showDeletedUsers();
    else if (this.isAllUsers) this.showAllUsers();
  }




  

  sizeGrid() {
    if (this.gridApi) {
      this.grid.autoSizeAll(this.gridApi, this.gridColumnApi, 220, true);
    }
  }




  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setRowData(this.selections.userSetup.rowData)
    this.sizeGrid();
    this.toastService.destroyToast();

    this.subscription = this.selections.newUserAndLoginEmitter.subscribe(res => {
      
      res.newValues = {
        name: null,
        nameShort: null,
        roleName: null,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
        sites: null,
        siteCode: null,
        email: null,
        employeeNumber: null
      };
      res.originalValues = {
        name: res.name,
        nameShort: res.nameShort,
        roleName: res.roleName,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
        sites: res.sites,
        siteCode: res.siteCode,
        email: this.constants.clone(res.email),
        employeeNumber: res.employeeNumber
      }

      if (res) {
        const index = this.selections.userSetup.rowData.findIndex(obj => obj.code === res.code);
        if (index !== -1) {
          this.selections.userSetup.rowData[index] = res;
        } else {
          this.selections.userSetup.rowData.push(res);
        }
      }
      this.gridApi.setRowData(this.selections.userSetup.rowData)
      this.gridApi.refreshCells()
      this.sizeGrid();

    })

  }

  onRowGroupOpened(params) {
    this.sizeGrid()
  }


  addUser(params?: any) {
    if (!this.constants.userRolePermissions.canMakeUserChanges) { return; }
    if (params) {
      this.constants.addUserModal.initiateExistingUser(params.data);
    } else {
      this.constants.addUserModal.initiateNewUser();
    }
    this.constants.addUserModal.showModal();
  }

  export() {

    this.toastService.loadingToast('Generating Excel file...')

    let tableRows: UserMaintenanceForExcel[] = [];

    this.gridApi.forEachNodeAfterFilter(n => {

      // If we have data:
      if (!!n.data) {

        let sites: string;

        if (n.data.sites) {
          sites = n.data.sites.split(',').length === this.constants.Sites.length - 1 ? 'All Sites' : n.data.sites;
        }

        if (sites && sites !== 'All Sites') {
          let siteNames: string = '';

          sites.split(',').forEach(siteId => {

            let siteName = this.constants.Sites.find(x => x.id === parseInt(siteId))?.description;

            // Prevents adding undefined inactive sites
            if(siteName != undefined)
            {
              siteNames += siteName + ', ';

            }
          })

          sites = siteNames.slice(0, -2);
        }

        let homeSite: string = this.constants.Sites.find(x => x.id === n.data.siteCode)?.description;

        let state: string;
        if (n.data.isDeleted) { state = 'Deleted'; }
        else if (n.data.isLocked) { state = 'Locked'; }
        else { state = 'Active'; }

        tableRows.push({
          Name: n.data.name,
          UserId: n.data.code,
          EmployeeNumber: n.data.employeeNumber,
          Role: n.data.roleName,
          Email: n.data.email,
          Sites: sites,
          HomeSite: homeSite,
          State: state
        })
      }
    })
    
    let sheet: SheetToExtractOld = {
      tableData: tableRows,
      tableName: 'StockPulse Users',
      columnWidths: [30, 10, 30, 20, 50, 70, 30, 10]
    }

    this.downloadExcelFile(sheet);
  }

  downloadExcelFile(sheet: SheetToExtractOld) {
    let workbook = new excelJS.Workbook();

    let imageId = workbook.addImage({
      base64: this.logo.provideStockPulseLogo(),
      extension: 'png'
    });

    try {
      //define worksheet
      let worksheet = workbook.addWorksheet(sheet.tableName)

      //generic stuff for worksheet
      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 3, zoomScale: 85 }
      ];

      //columns things
      let columns = []
      sheet.columnWidths.forEach(w => {
        columns.push({ width: w })
      })
      worksheet.columns = columns;


      //rows
      let titleRow = worksheet.addRow([sheet.tableName])//title
      titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };
      worksheet.addRow([]);//blank


      //the table headerRow      
      let tableHeaderRow = worksheet.addRow(Object.keys(sheet.tableData[0]))
      let colCount = Object.keys(sheet.tableData[0]).length

      //loop through each column in active range and colour cells
      for (let i = 0; i < colCount; i++) {
        let colLetter = String.fromCharCode(65 + i)
        worksheet.getCell(colLetter + '3').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '3').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
      }

      //the data rows
      sheet.tableData.forEach(x => {
        let newRow = worksheet.addRow(Object.values(x))
      })

      //loop through the first cell of each row and colour
      let rowCount = worksheet.rowCount + 1;
      for (let i = 4; i < rowCount; i++) {
        worksheet.getCell('A' + i.toString()).font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell('A' + i.toString()).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
      }

      //images
      let columnCount = worksheet.columns.length
      worksheet.addImage(imageId, {

        tl: { col: columnCount - 1, row: 0 },
        //br: {col: 13, row: 2},
        ext: { width: 168, height: 36 },
        editAs: 'absolute'
      });
    }
    catch (e) {
      //carry on
    }

    let workbookName = 'StockPulse Extract ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });

    this.toastService.destroyToast();
  }

  public onFileChange(evt: any) {

    this.fileToUpload = null;
    this.fileReadyToUpload = true;
    try {

      const target: DataTransfer = <DataTransfer>(evt.target);
      const allFiles = Array.from(target.files);
      let processedFileCount = 0;
      this.fileToUpload = allFiles[0];
    }
    catch (e) {
      //this.constants.toastDanger(`Failed whilst loading file - ${e}`)
      //this.constants.spinnerEvents.next({ show: false })
    }

  }

  uploadFile() {
   
    //check file size
    let totalSize = this.fileToUpload.size;


    if (totalSize < 10000000) //10mb
    {
      this.uploadfile(this.fileToUpload);
    }
    else {
      this.toastService.errorToast('Maximum upload limit exceeded')// this.message = 'Upload success.';
    }
   
  }

  uploadfile(users: any) {
    this.toastService.loadingToast("Creating users...");
    const formData = new FormData();
    
    formData.append("file", users);
    
    this.apiAccess.postWithOptions('User', 'UserAndLogin/Upload', formData, { reportProgress: true, observe: 'events' }).subscribe(event => {
      if (event.type === HttpEventType.UploadProgress)
        this.progress = Math.round(100 * event.loaded / event.total);
      else if (event.type === HttpEventType.Response) {
        this.fileReadyToUpload = false;
        this.fileToUpload = null;
        //this.refreshData();
      }
    }, e => {
      this.toastService.destroyToast();
      //this.toastService.errorToast('Failed to create users');
      this.toastService.errorToast(e);
    },()=>{
      //finished
      this.toastService.destroyToast();
      this.toastService.successToast('Successfully created users');
      this.getUserAndLoginsData();
    })
  }

  refreshUserCache(){
    this.apiAccess.get('User','ReloadCache').subscribe(()=>{
      this.toastService.successToast('Successfully reloaded cache');
    });
  }

  showRestrictedButtons() {
    return this.selections.userUsername?.includes('@cphi.co.uk') || this.selections.userUsername?.includes('@cphinsight.com');
  }
}
