import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { Subscription } from "dexie";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { ConstantsService } from '../../../services/constants.service';
import { IconService } from '../../../services/icon.service';
import { SelectionsService } from '../../../services/selections.service';
import { ReviewImportMapsModalService } from "./reviewImportMapsModal.service";

@Component({
    selector: 'deleteImportMapButton-cell',
    template: `
        <button *ngIf="canDeleteMask()" class="btn btn-danger" (click)="maybeDelete()">
            <fa-icon [icon]="icon.faTrash">Delete</fa-icon>
        </button>    
    `,
    styles: [
        `
        button {
            height: 1.6em;
            margin: 0px;
            margin-top: -5px;
            padding: 0px 10px;
            opacity: 0.2;
        }

        .btn:hover {
            opacity: 1;
        }
      `
    ],
    standalone: false
})
export class DeleteImportMapButtonComponent implements ICellRendererAngularComp {
    params: any;

    constructor(
        public selections: SelectionsService,
        public icon: IconService,
        public constants: ConstantsService,
        public apiAccess: ApiAccessService,
        public toastService: ToastService,
        public service: ReviewImportMapsModalService
    ) { }


    agInit(params: any): void {
        this.params = params;
    }

    refresh(): boolean {
        return false;
    }

    maybeDelete() {
        this.constants.confirmModal.confirmModalHeader = 'Are you sure you want to delete this import map?';
        this.constants.confirmModal.isDestructive = true;

        const mySubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
            if (res) { this.delete(); }
            mySubscription.unsubscribe();
        })

        this.constants.confirmModal.showModal();
    }

    delete() {
        this.apiAccess.deleteImportMask(this.params.data.id).subscribe(res => {
            this.toastService.successToast('Import map deleted');

            this.service.importMasks = this.service.importMasks.filter(x => x.id !== this.params.data.id);
            this.service.importMasksCopy = JSON.parse(JSON.stringify(this.service.importMasks));

            this.params.api.setRowData(this.service.importMasks);

        }, e => {
            this.toastService.errorToast('Failed to delete import map');
        })
    }

    canDeleteMask() {
        if (!this.service.editing) return false;
        if (this.params.data.userId !== this.selections.userId && this.constants.userRolePermissions.canEditImportMaps) return false;
        return true;
    }
}
