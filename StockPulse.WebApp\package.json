{"name": "stockpuls<PERSON><PERSON>", "version": "3.5.2", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "dev": "ng build --configuration=dev", "prod": "ng build --configuration=production && npx brotli-cli compress -q 11 ./dist/**/* --glob && del /s /q dist\\config\\*.br 2> nul"}, "private": true, "dependencies": {"@angular/animations": "^20.0.6", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.6", "@angular/compiler": "^20.0.6", "@angular/core": "^20.0.6", "@angular/forms": "^20.0.6", "@angular/google-maps": "^20.0.4", "@angular/platform-browser": "^20.0.6", "@angular/platform-browser-dynamic": "^20.0.6", "@angular/platform-server": "^20.0.6", "@angular/router": "^20.0.6", "@azure/msal-angular": "^4.0.17", "@azure/msal-browser": "^4.19.0", "@bluehalo/ngx-leaflet": "^20.0.0", "@fortawesome/angular-fontawesome": "^2.0.1", "@fortawesome/fontawesome-svg-core": "6.3.0", "@fortawesome/free-regular-svg-icons": "6.3.0", "@fortawesome/free-solid-svg-icons": "6.3.0", "@fortawesome/pro-light-svg-icons": "6.3.0", "@fortawesome/pro-regular-svg-icons": "6.3.0", "@fortawesome/pro-solid-svg-icons": "6.3.0", "@microsoft/applicationinsights-web": "^2.8.6", "@ng-bootstrap/ng-bootstrap": "^19.0.1", "@ngneat/hot-toast": "^5.0.1", "@ngneat/overview": "^4.1.0", "@popperjs/core": "^2.11.8", "add": "^2.0.6", "ag-grid-angular": "29.1.0", "ag-grid-community": "29.1.0", "animate.css": "4.1.1", "bootstrap": "5.3.2", "brotli": "^1.3.3", "canvas-confetti": "^1.6.0", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "core-js": "^3.1.3", "dexie": "3.2.3", "eslint": "^8.13.0", "exceljs": "4.3.0", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "rxjs": "7.8.0", "sass": "^1.58.3", "tslib": "^2.0.0", "xlsx": "0.18.5", "yarn": "^1.15.2", "zone.js": "0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.6", "@angular/language-service": "^20.0.6", "@angular/localize": "^20.0.6", "@fortawesome/fontawesome-pro": "6.3.0", "@types/googlemaps": "^3.39.13", "@types/jasmine": "4.3.1", "@types/jasminewd2": "~2.0.3", "@types/node": "18.14.0", "codelyzer": "^6.0.0", "jasmine-core": "4.5.0", "jasmine-spec-reporter": "7.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.0.0", "protractor": "~7.0.0", "rxjs-tslint": "^0.1.7", "ts-node": "10.9.1", "typescript": "5.8.3", "webpack": "5.75.0", "webpack-bundle-analyzer": "4.8.0"}}