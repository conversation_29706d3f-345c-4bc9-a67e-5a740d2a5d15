import { Component, OnInit } from '@angular/core';
import { SiteVMWithSelected } from 'src/app/model/SiteVMWithSelected';
import { StockCheck } from 'src/app/model/StockCheck';
import { StockChecksService } from 'src/app/pages/stockChecks/stockChecks.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
    selector: 'singleSitePickerWithSearch',
    templateUrl: './singleSitePickerWithSearch.component.html',
    styleUrls: ['./singleSitePickerWithSearch.component.scss'],
    standalone: false
})
export class SingleSitePickerWithSearchComponent implements OnInit {
  stockChecksCopy: StockCheck[];
  searchString: string = '';

  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public stockChecksService: StockChecksService
  ) {}

  ngOnInit(): void {
    this.reset();
  }

  reset() {
    this.stockChecksCopy = JSON.parse(JSON.stringify(this.stockChecksService.stockCheckVMs));
    this.searchString = '';
  }

  searchList() {
    let stockChecks: StockCheck[] = JSON.parse(JSON.stringify(this.stockChecksService.stockCheckVMs));
    this.stockChecksCopy = stockChecks.filter(s => s.site.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }

  selectStockCheck(stockCheck: StockCheck) {
    this.stockChecksService.loadStockCheck(stockCheck, true, true);
  }

  formatUTC(date: string) {
    return new Date(date).toISOString().split('T')[0];
  }
}
