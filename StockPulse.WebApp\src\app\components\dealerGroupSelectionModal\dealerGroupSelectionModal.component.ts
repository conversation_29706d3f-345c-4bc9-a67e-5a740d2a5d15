import { Component, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from '../../services/selections.service';
import { ConstantsService } from "src/app/services/constants.service";
import { DealerGroupVM } from "src/app/model/DealerGroupVM";
import { BaseURLVM } from "src/app/model/BaseURLVM";

@Component({
    selector: 'dealerGroupSelectionModal',
    templateUrl: './dealerGroupSelectionModal.component.html',
    styleUrls: ['./dealerGroupSelectionModal.component.scss'],
    standalone: false
})
export class DealerGroupSelectionModalComponent {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  modalHeader: string;
  isDestructive:boolean;

  

  set baseURLVMs(baseURLVMs: BaseURLVM[]) {
    localStorage.setItem("allDealerGroups", JSON.stringify(baseURLVMs));
  }

  get getBaseURLVMs(): BaseURLVM[] {
    const data = localStorage.getItem("allDealerGroups");
    if (!data) {
      return [];
    }
  
    const baseURLVMs: BaseURLVM[] = JSON.parse(data);
  
    const seen = new Set<string>();
  
    baseURLVMs.forEach(baseURLVM => {
      baseURLVM.dealerGroupVMs = baseURLVM.dealerGroupVMs.filter(dg => {
        const key = `${dg.id}|${dg.description}`;
        if (seen.has(key)) {
          return false; // duplicate across any array → remove
        }
        seen.add(key);
        return true; // first time seen → keep
      });
    });
  
    return baseURLVMs;
  }
  
  
  


  constructor(
    public selections: SelectionsService,
    public modalService: NgbModal,
    public constants: ConstantsService,
  ) { 

    this.modalHeader = "Select a dealer group to continue";
  }

  showModal() {
    this.modalService.open(this.modalRef, { }).result.then((result) => {
 //     this.selections.countrySelectionModalEmitter.next(this.selectedCountry);
    }, (reason) => {
   //   this.selections.countrySelectionModalEmitter.next(this.selectedCountry);
    });
  }

  setBaseURL(baseURL: string, dealerGroupVM: DealerGroupVM){
    console.log('selected baseURL', baseURL);
    localStorage.setItem('baseURL', baseURL);
    localStorage.setItem('isDealerGroupSelected', 'true');
    localStorage.setItem('selectedDealerGroup', dealerGroupVM.description);
    //console.log('this.constants.baseURL',this.constants.baseURL);
    this.modalService.dismissAll();
    this.selections.dealerGroupSelectionModalEmitter.next(dealerGroupVM);
    //window.location.reload();
  }
}
