﻿using System.Reflection;
using System;

namespace StockPulse.WebApi.ViewModel
{
    public class StockCheckSummaryItem
    {
        public int Id { get; set; }
        public string Site {  get; set; }
        public string Status { get; set; }
        public int MissingUnresolved {  get; set; }
        public int UnknownUnresolved { get; set; }
        public int StockItems { get; set; }
        public int Scans { get; set; }
        public decimal PercentageComplete
        {
            get
            {
                if (StockItems == 0 && Scans == 0) return 0;
                if (StockItems == 0) return Math.Max(1 - (decimal)(UnknownUnresolved + MissingUnresolved) / (Scans > 0 ? Scans : 1), 0);
                return (decimal)Math.Max(1 - ((double)(UnknownUnresolved + MissingUnresolved) / Math.Max(StockItems, Scans)), 0);
            }
        }
        public DateTime StockCheckDate { get; set; }
    }
}
