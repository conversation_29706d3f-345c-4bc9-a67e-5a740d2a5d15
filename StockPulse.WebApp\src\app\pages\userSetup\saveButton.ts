import { Component, ViewChild } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";
import { RowNode } from 'ag-grid-community';
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { UserAndLogin } from "../../model/UserAndLogin";
import { ConstantsService } from '../../services/constants.service';
import { IconService } from '../../services/icon.service';
import { SaveDataService } from '../../services/saveData.service';
import { SelectionsService } from '../../services/selections.service';

interface descriptionAndName {
    description: string;
    name: string;
    isPriceRoR: boolean;
}

// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'saveButtonTarget-cell',
    template: `
   
        <button *ngIf="params.data?.hasChanged" class="btn btn-success" (click)="saveUser()">
            <fa-icon [icon]="icon.faSave"> Save </fa-icon>
            </button>    

    

    `,
    styles: [
        `
        button{
            height: 1.6em;
            margin: 0px;
            margin-top: -5px;
            padding: 0px 10px;

        }
       
      `
    ],
    standalone: false
})
export class SaveButtonUserComponent implements ICellRendererAngularComp {
   
params:any;


    constructor(
        public selections: SelectionsService,
        public icon: IconService,
        public save: SaveDataService,
        public constants: ConstantsService,
        public apiAccess: ApiAccessService,
        public toastService: ToastService
    ) { }


    agInit(params: any): void {

    this.params = params

    }
    refresh(): boolean {
        return false;
    }


    saveUser(){
        let userAndLogin:UserAndLogin = this.params.data;

        //build new object
        let itemToPersist: UserAndLogin = {
            code: userAndLogin.code,
            name:userAndLogin.newValues.name || userAndLogin.name,
            nameShort:userAndLogin.newValues.nameShort || userAndLogin.nameShort,
            userName:userAndLogin.newValues.email,
            roleName:userAndLogin.newValues.roleName || userAndLogin.roleName,
            email:userAndLogin.originalValues.email,
            sites:userAndLogin.newValues.sites || userAndLogin.sites,
            siteCode:userAndLogin.newValues.siteCode || userAndLogin.siteCode,
            newEmail: userAndLogin.newValues.email,
            employeeNumber: userAndLogin.newValues.employeeNumber || userAndLogin.employeeNumber,
            isDeleted: userAndLogin.isDeleted,
            isLocked: userAndLogin.isLocked
        }

        //return this.http.put(this.constants.baseURL + '/api/UserAndLogin', user, this.getOptions())
        this.apiAccess.patch('User','UserAndLogin',itemToPersist).subscribe((data) => {
                 //success!

            //put new values to existing
            userAndLogin.name=userAndLogin.newValues.name || userAndLogin.name,
            userAndLogin.nameShort=userAndLogin.newValues.nameShort || userAndLogin.nameShort,
            userAndLogin.roleName=userAndLogin.newValues.roleName || userAndLogin.roleName,
            userAndLogin.sites=userAndLogin.newValues.sites || userAndLogin.sites,
            userAndLogin.siteCode=userAndLogin.newValues.siteCode || userAndLogin.siteCode,
            userAndLogin.email=userAndLogin.newValues.email || userAndLogin.email,
            userAndLogin.employeeNumber=userAndLogin.newValues.employeeNumber || userAndLogin.employeeNumber;

            userAndLogin.newValues = {
                name: null,
                nameShort: null,
                roleName: null,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
                sites: null,
                siteCode: null,
                email: null,
                employeeNumber: null
            }
            userAndLogin.hasChanged = false;
            this.params.api.redrawRows()

            this.toastService.successToast('Saved User');

        },(e)=>{
            this.toastService.errorToast('Failed to save User')
        })
        

    }

}


