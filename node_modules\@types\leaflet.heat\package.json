{"name": "@types/leaflet.heat", "version": "0.2.4", "description": "TypeScript definitions for leaflet.heat", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet.heat", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "onder<PERSON><PERSON>", "url": "https://github.com/onderceylan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet.heat"}, "scripts": {}, "dependencies": {"@types/leaflet": "*"}, "typesPublisherContentHash": "def6045b81ee6295d5249e2b3f8e83478a540b8db1f83cf3a2c38d8abd4dad5e", "typeScriptVersion": "4.5"}