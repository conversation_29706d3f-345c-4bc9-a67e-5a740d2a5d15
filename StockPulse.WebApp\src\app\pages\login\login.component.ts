import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MsalGuardConfiguration, MsalService, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { AuthenticationResult, InteractionType, PopupRequest, RedirectRequest } from '@azure/msal-browser';
import { Subscription, forkJoin } from 'rxjs';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { AppStartService } from 'src/app/services/appStart.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { formItemHasAtSymbolValidator } from './validator';
import { DealerGroupSelectionModalComponent } from 'src/app/components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from 'src/app/services/selections.service';
import { MultiDealerGroupService } from 'src/app/services/multiDealerGroup.service';
import { BaseURLVM } from 'src/app/model/BaseURLVM';
import { LoginService } from './login.service';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss', './../../../styles/accountPage.scss'],
    standalone: false
})
export class LoginComponent implements OnInit {

  loginFormGroup: FormGroup;

  loginError: boolean;
  appDataLoadSubscription: Subscription;

  isRefByAuthRedirect: boolean;
  haveSubmittedForm: boolean;


  @ViewChild('dealerGroupSelectionModal', { static: true }) dealerGroupSelectionModal: DealerGroupSelectionModalComponent;


  constructor(
    //private data: GetDataService, 
    private formBuilder: FormBuilder,
    private router: Router,
    public icon: IconService,
    private apiAccess: ApiAccessService,
    private appStartService: AppStartService,
    private constants: ConstantsService,
    public toastService: ToastService,
    public modalService: NgbModal,
    public selections: SelectionsService,
    public multiDealerGroupService: MultiDealerGroupService,

    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private authService: MsalService,
    public loginService: LoginService
  ) {
    this.createForm();
    
  }

  resetMultiCountrySettings(){
    this.constants.isMultiDealerGroupAccount = false;
  }

  ngOnInit(): void {

    if (document.referrer.endsWith('auth')) {
      console.log('ref by login');
      this.isRefByAuthRedirect = true;
    }
    else {
      this.isRefByAuthRedirect = false;
    }

    // Detect if form autofilled, then un-disable login button
    setTimeout(() => {
      let emailfield: HTMLInputElement = document.getElementById('email') as HTMLInputElement;
      let loginButton: HTMLInputElement = document.getElementById('loginButton') as HTMLInputElement;

      [":autofill", ":-webkit-autofill", ":-moz-autofill"].some((selector) => {
        try {
          if (emailfield.matches(selector)) {
            loginButton.disabled = false;
          }
        } catch {
          return false;
        }
      });
    }, 750)
  }


  createForm() {
    this.loginFormGroup = this.formBuilder.group({
      email: ['', [Validators.required, formItemHasAtSymbolValidator()]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    })
  }


  test() {
    console.log(this.loginFormGroup)
  }


  public submit(): void {
    this.resetMultiCountrySettings();

    if (this.loginFormGroup.valid) {
      let email = this.loginFormGroup.get('email').value;
      let password = this.loginFormGroup.get('password').value;

      let requests = [];
      requests = this.multiDealerGroupService.createRequests(email);
      
    
      forkJoin(requests).subscribe((data: BaseURLVM[]) => {
        //add more country reults here
        const baseURLUK: BaseURLVM = data[0];
        const baseURLUS: BaseURLVM = data[1];
        
        console.log(baseURLUK, baseURLUS);

        //add more country checks here 
        this.multiDealerGroupService.checkandSetBaseURL(baseURLUK, baseURLUS)
       
        if (this.constants.isMultiDealerGroupAccount){
          this.setDealerGroupAccountAndLogin(email, password);
        }
        else {
          this.continueWithLogin(email, password);
        }

      })
      
    }

  }

  setDealerGroupAccountAndLogin(email: string, password: string){
      
    let mySubscription = this.selections.dealerGroupSelectionModalEmitter.subscribe(res =>{
        if(res){
          this.continueWithLogin(email, password);
        }
        mySubscription.unsubscribe();
    })

    this.constants.dealerGroupSelectionModal.showModal();
   
    }

    continueWithLogin(email: string, password: string){
      this.haveSubmittedForm = true;
      var selectedDealerGroupId = localStorage.getItem('accessToken');
          this.apiAccess.post('account', 'login', { username: email, password: password, dealerGroupName: this.constants.getDealerGroupName() }).subscribe(
            (res: any) => {
              if (res.error && res.error == "Incorrect username or password.") {
                this.toastService.errorToast('Incorrect email or password')
                //this.loginError = true;
              }
              else if (res.error) {
                this.toastService.errorToast(res.error)
              }
              else if (res.usersRole === "Scanner") {
                this.loginService.incorrectAccessRights = true;
              }
              else {
                localStorage.setItem('accessToken', res.accessToken);
                localStorage.setItem('refreshToken', res.refreshToken.token);
                this.constants.accessToken = res.accessToken;
                this.constants.refreshToken = res.refreshToken.token;
                
                //this.selections.refreshToken = res.refreshToken;
                this.constants.userLoggedInThroughAzureAD = false;
                this.appStartService.initiateAppDataLoad();
              }
            },
            err => {
              this.toastService.errorToast('Incorrect email or password')
            }, () => {
              this.haveSubmittedForm = false;
            }
          );

    }

  loginWithAzureAD() {

    //Clear old auth detals
    localStorage.clear();

    if (this.msalGuardConfig.interactionType === InteractionType.Popup) {
      if (this.msalGuardConfig.authRequest) {
        this.authService.loginPopup({ ...this.msalGuardConfig.authRequest } as PopupRequest)
          .subscribe((response: AuthenticationResult) => {
            console.log('setting active account 1 ');
            this.authService.instance.setActiveAccount(response.account);
          });
      } else {
        this.authService.loginPopup()
          .subscribe((response: AuthenticationResult) => {
            console.log('setting active account 2 ');
            this.authService.instance.setActiveAccount(response.account);
          });
      }
    } else {
      console.log(this.msalGuardConfig.authRequest);
      if (this.msalGuardConfig.authRequest) {
        console.log('here 1',this.msalGuardConfig.authRequest);
        this.authService.loginRedirect({ ...this.msalGuardConfig.authRequest } as RedirectRequest);
      } else {
        console.log('here 2');
        //this.authService.loginRedirect()
      }


    }
  }


  redirectToForgotPassword() {
    this.router.navigateByUrl('/forgotpassword');
  }

}
