CREATE OR ALTER PROCEDURE [dbo].[GET_LatestScanDate]  
(
    @UserId INT
)  
AS  
BEGIN

  SET NOCOUNT ON;

  DECLARE @DealerGroupId INT;
  SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

  SELECT
    TOP 1 s.ScanDateTime
    FROM Scans s
	INNER JOIN StockChecks sc ON sc.Id = s.StockCheckId
	INNER JOIN Sites si ON si.Id = sc.SiteId	
	INNER JOIN Divisions d ON d.Id = si.DivisionId
    WHERE 
        d.DealerGroupId = @DealerGroupId
        AND sc.IsActive = 1
    ORDER BY s.ScanDateTime DESC
END;
