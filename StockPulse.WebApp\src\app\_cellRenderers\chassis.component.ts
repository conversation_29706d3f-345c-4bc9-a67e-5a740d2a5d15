import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";

// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'chassis-cell',
    template: `
    
    <div class="chassis" [ngClass]="{ 'strikeThrough': !params }">
    <span *ngIf="params">{{params}}</span>
    <span *ngIf="!params" style="opacity: 0;">00000000</span>
    
  </div>
  
  `,
    styles: [],
    standalone: false
})
export class ChassisComponent implements ICellRendererAngularComp {
    
    params: number = 0;

    agInit(params: any): void {
    
    this.params = params.value;
   

    }

    refresh(): boolean {
        return false;
    }
}


