import {
  Component,
  Input,
  SimpleChanges,
  OnInit,
  OnDestroy,
} from "@angular/core";
import { MapOptions, Map, LeafletEvent } from "leaflet";
import { Subscription } from "rxjs";
import * as L from "leaflet";
import "leaflet.heat";
import { DashboardService } from "../../dashboard.service";

// Define the HeatLayer type
interface HeatLayer extends L.Layer {
  addTo(map: Map): this;
  setLatLngs(latlngs: any[]): this;
  redraw(): this;
}

@Component({
  selector: "heatmap",
  templateUrl: "./heatmap.component.html",
  styleUrls: ["./heatmap.component.scss"],
  standalone: false,
})
export class HeatmapComponent implements OnInit, OnDestroy {
  options: MapOptions = {
    layers: [
      L.tileLayer(
        "https://cartodb-basemaps-{s}.global.ssl.fastly.net/rastertiles/voyager/{z}/{x}/{y}.png"
      ),
    ],
  };

  map: Map;
  heatLayer: HeatLayer;

  private subscription: Subscription;

  constructor(public dashboardService: DashboardService) {}

  get markers(): [number, number][] {
    return this.dashboardService.dashboard.heatmapItems.filter(
      ([lat, lon]) => lat !== 0 && lon !== 0
    );
  }

  ngOnInit(): void {
    // Subscribe to the newDataEmitter
    this.subscription = this.dashboardService.newDataEmitter.subscribe(() => {
      this.reactToChanges();
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  reactToChanges(): void {
    if (this.heatLayer && this.map) {
      this.map.removeLayer(this.heatLayer);

      this.heatLayer = (L as any)
        .heatLayer(this.markers, {
          blur: 12,
          minOpacity: 0.6,
          radius: 20,
        })
        .addTo(this.map);

      if (
        !this.dashboardService.userHasInteractedWithMap &&
        this.markers?.length > 0
      ) {
        this.map.fitBounds(L.latLngBounds(this.markers));
      }
    }
  }

  onMapReady(map: Map): void {
    this.heatLayer = (L as any)
      .heatLayer(this.markers, {
        blur: 12,
        minOpacity: 0.6,
        radius: 20,
      })
      .addTo(map);

    map.attributionControl.setPrefix("");

    if (!this.dashboardService.userHasInteractedWithMap) {
      map.fitBounds(L.latLngBounds(this.markers));
    }

    const mapContainer = map.getContainer();

    const markUserInteraction = () => {
      if (!this.dashboardService.userHasInteractedWithMap) {
        this.dashboardService.userHasInteractedWithMap = true;
      }
    };

    mapContainer.addEventListener("wheel", markUserInteraction);
    mapContainer.addEventListener("mousedown", markUserInteraction);

    const zoomInBtn = mapContainer.querySelector(".leaflet-control-zoom-in");
    const zoomOutBtn = mapContainer.querySelector(".leaflet-control-zoom-out");

    zoomInBtn?.addEventListener("click", markUserInteraction);
    zoomOutBtn?.addEventListener("click", markUserInteraction);

    this.map = map;
  }

  fitBounds(): void {
    this.map.fitBounds(L.latLngBounds(this.markers));
    setTimeout(() => {
      this.dashboardService.userHasInteractedWithMap = false;
    }, 250);
  }
}
