CREATE OR ALTER PROCEDURE [dbo].[GET_DataLoadedBasicInfoItems]
(
    @UserId INT = NULL,
    @IsActive BIT,
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)  
AS  
BEGIN
    SET NOCOUNT ON;    

    DECLARE @DealerGroupId INT;
    SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

    SELECT
        sc.Id AS StockCheckId,
        s.Description AS Site,
        d.Description AS Division,
        sc.Date AS StockCheckDate,
        st.Description AS Status,
        cu.Name AS CompletedBy,

        (
            SELECT COUNT(*) 
            FROM StockItems si 
            WHERE si.StockCheckId = sc.Id
        ) AS InStock,

        (
            SELECT COUNT(*) 
            FROM Scans sca 
            WHERE sca.StockCheckId = sc.Id
        ) AS Scans

    FROM StockChecks sc
    INNER JOIN Sites s ON s.Id = sc.SiteId
    INNER JOIN Statuses st ON st.Id = sc.StatusId
    INNER JOIN Divisions d ON d.Id = s.DivisionId
    LEFT JOIN Users cu ON cu.Id = sc.ApprovedByAccountantId

    WHERE
        (@IsActive IS NULL OR SC.IsActive = @IsActive) AND
        d.DealerGroupId = @DealerGroupId AND
        sc.IsRegional = 0 AND
        sc.IsTotal = 0 AND
        (@FromDate IS NULL OR sc.Date >= @FromDate) AND
        (@ToDate IS NULL OR sc.Date <= @ToDate)

    GROUP BY
        s.Id, s.Description, st.Description, cu.Name, sc.Date, sc.Id, d.Description

    ORDER BY
        sc.[Date] DESC, s.[Description]
END
GO
