﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using StockPulse.Repository.Database;

namespace StockPulse.Loader.Services
{
    public class LithiaTBLoaderService : GenericLoaderJobServiceParams
    {
        private HashSet<string> validStrings;

        // Constructor
        public LithiaTBLoaderService()
        {

        }

        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "lithia";
            string filePattern = "*GLTrailBalance*.csv";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.LithiaTB,
                customerFolder = customer,
                filename = filePattern,
                importSPName = null,
                loadingTableName = "FinancialLines",
                jobName = "LithiaTB",
                pulse = PulsesService.STK_LithiaTB,
                fileType = FileType.csv,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                headerDefinitions = null,
                errorCount = 0,
                dealerGroupId = 10,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
                isUS = true
            };

            return parms;
        }

        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            List<Model.Input.FinancialLine> incomingLines = new List<Model.Input.FinancialLine>(10000);  //preset the list size
            int incomingProcessCount = 0;

            validStrings = new HashSet<string>
            {
                "23200", "23210", //NEW 
                "24000", "24010", "24020", "24030", "24040", "24045", //USED
                "24070", "24080", "24090", "24095", //PROGRAM
                "26100", "26110", //RENTAL
                "26150", "26160", //LOANER
                "23206" //FLEET
            };

            Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

            using (var db = new StockpulseContext(true))
            {
               // Main sites
               List<Site> sites = db.Sites
                   .AsNoTracking()
                   .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId && s.IsActive)
                   .ToList();

               // Build valid SiteId set
               HashSet<int> validSiteIds = sites.Select(s => s.Id).ToHashSet();

               var siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId && validSiteIds.Contains(x.SiteId))
                                                                            .AsEnumerable().ToDictionary(x => x.Description, x => x);

                int total = rowsAndHeaders.rowsAndCells.Count;

                Parallel.ForEach(rowsAndHeaders.rowsAndCells.Skip(1), (rowCols, state, index) =>
                {
                                        incomingProcessCount++;

                                        System.Console.WriteLine($"Count {incomingProcessCount} / {total}");
                    try
                    {
                        string code = rowCols[4].ToString().Trim();

                        if (!validStrings.Contains(code)) return;

                        string siteName = rowCols[3].Trim().ToString();

                        // Attempt to get matching Site from the dictionary
                        if (SharedLoaderService.SkipSiteForLithiaUS(siteName))
                        {
                            return;
                        }

                        if (!siteDescriptionDictionary.TryGetValue(siteName, out var siteDictionary) || !siteDictionary.IsPrimarySiteId)
                        {
                            parms.errorCount++;

                            if (siteName == "" || siteName == null)
                            {
                               siteName = "[BLANK NAME]";
                            }

                            if (!missingSitesDictionary.ContainsKey(siteName))
                            {
                               missingSitesDictionary[siteName] = 1;
                            }
                            else
                            {
                               missingSitesDictionary[siteName] += 1;
                            }

                            return;
                        }

                        int siteId = siteDictionary.SiteId;

                        var incomingLine = new Model.Input.FinancialLine()
                        {
                            SiteId = siteId,
                            Code = code,
                            AccountDescription = rowCols[5].ToString(),
                            Balance = Decimal.Parse(rowCols[8].ToString()),
                            FileImportId = parms.fileImportId,
                            DealerGroupId = parms.dealerGroupId,
                        };

                        lock (incomingLines)
                        {
                            incomingLines.Add(incomingLine);
                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes += $" failed on adding item, Item: {index + 1} {err} <br>";
                        parms.errorCount++;
                    }
                });
            }

            missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            foreach (var item in missingSitesDictionary)
            {
               logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
            }

            DataTable result = incomingLines.ToDataTable();
            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("DealerGroup");

            return result;
        }

        public async Task BulkInsertAsync(DataTable dataTable, string connectionString)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "FinancialLines";
                    await bulkCopy.WriteToServerAsync(dataTable);
                }
            }
        }
    }
}
