import * as utilities from "../services/utilityFunctions";

export class StockCheckSpeedItem {
   constructor(data: StockCheckSpeedItem) {
      Object.assign(this, data);
      if (this.stockCheckDate) {
         this.stockCheckDate = new Date(this.stockCheckDate);
      }

      if (this.stockLoaded) {
         this.stockLoaded = new Date(this.stockLoaded);
      }

      if (this.firstScan) {
         this.firstScan = new Date(this.firstScan);
      }
      if (this.lastScan) {
         this.lastScan = new Date(this.lastScan);
      }
      if (this.reconciliationCompleted) {
         this.reconciliationCompleted = new Date(this.reconciliationCompleted);
      }
      if (this.reconciliationApproved) {
         this.reconciliationApproved = new Date(this.reconciliationApproved);
      }
   }

   stockCheckId: number;
   site: string;
   division: string;
   stockCheckDate: Date;
   status: string;
   completedBy: string;
   approvedBy: string;
   stockLoaded: Date;
   scans: number;
   firstScan: Date;
   lastScan: Date;
   reconciliationCompleted: Date;
   timeToComplete: string;
   reconciliationApproved: Date;
   timeToApprove: string;
   totalDuration: string;
   timeToResolveUnknowns: string;
   timeToResolveMissings: string;
   scanDurationHours:number;
   timeToCompleteHours:number;
   timeToApproveHours:number;
  totalDurationHours:number;
  timeToResolveUnknownsHours:number;
  timeToResolveMissingsHours:number;

 
}
