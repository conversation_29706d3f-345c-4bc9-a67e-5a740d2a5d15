import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseURLVM } from 'src/app/model/BaseURLVM';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { MultiDealerGroupService } from 'src/app/services/multiDealerGroup.service';
import { ToastService } from 'src/app/services/newToast.service';

@Component({
    selector: 'appResetPassword',
    templateUrl: './resetPassword.component.html',
    styleUrls: ['./resetPassword.component.scss', './../../../styles/accountPage.scss'],
    standalone: false
})
export class ResetPasswordComponent implements OnInit {

  resetSuccess: boolean;
  resetFailed: boolean;
  passwordMatchFailed: boolean;
  passwordcriteriaFailed: boolean;
  usernameCriteriaFailed: boolean;

  passwordResetFormGroup = new UntypedFormGroup({
    userName: new UntypedFormControl('', Validators.required),
    password: new UntypedFormControl('', Validators.required),
    confirmPassword: new UntypedFormControl('', Validators.required),
  });

  showMobileView: boolean;
  

  constructor(
    private apiAccess: ApiAccessService,
    private router: Router,
    private route: ActivatedRoute,
    public icon: IconService,
    public multiDealerGroupService: MultiDealerGroupService,
    public constants: ConstantsService,
    public toastService: ToastService,
  ) { }


  checkPasswords() { 
    let pass = this.passwordResetFormGroup.get('password').value;
    let confirmPass = this.passwordResetFormGroup.get('confirmPassword').value;

    if (pass === confirmPass) {
      this.passwordMatchFailed = false;
      return true;
    }
    else {
      this.passwordMatchFailed = true;
      return false;
    }
    //return pass === confirmPass ? null : { notSame: true }     
  }

  checkPasswordcriteria() {
    let pass = this.passwordResetFormGroup.get('password').value as string;
    const lowerLetters = /[a-z]+/.test(pass);
    const upperLetters = /[A-Z]+/.test(pass);
    const len = pass.length;

    if (lowerLetters && upperLetters && len > 11) {
      this.passwordcriteriaFailed = false;
      return true;
    }
    else {
      this.passwordcriteriaFailed = true;
      return false;
    };


  }
  

  ngOnInit(): void {
    localStorage.clear();
    this.showMobileView = window.innerWidth < 500;
  }

  public Submit(): void {

    if (this.passwordResetFormGroup.valid && this.checkPasswords() && this.checkPasswordcriteria()) {
      let u = this.passwordResetFormGroup.get('userName').value,
          p = this.passwordResetFormGroup.get('password').value,
          cp = this.passwordResetFormGroup.get('confirmPassword').value,
          t = this.route.snapshot.queryParamMap.get('token'),
          c = this.route.snapshot.queryParamMap.get('country'),
          e = this.route.snapshot.queryParamMap.get('email');
      
      t = t.split(' ').join('+');

      const l = {username: u, password: p, confirmPassword: cp, token: t};

      this.multiDealerGroupService.setBaseURLByCountryAndResetPassword(c,e).subscribe((data: any) => {
        //add more country reults here
        const baseURLVM: BaseURLVM = data[0];
  
        if (baseURLVM.baseURL.startsWith('https://')) {
          localStorage.setItem('baseURL', baseURLVM.baseURL);
        }

        this.apiAccess.post('account', 'ResetPassword', l).subscribe((res: any) => {
          if (res?.error) {
            this.toastService.errorToast(res.error)
          }
          else {
            this.resetSuccess = true;
            this.resetFailed = false;
          }
        }, err => {
          this.resetSuccess = false;
          this.resetFailed = true;
        });

  
  
      });

      
    }
  }

  RedirectToLogin(){
    this.router.navigateByUrl('/login');

  }

  validateUsername(event: any) {
    if (event.target.value.match(/^['a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/) && event.target.value.includes('.')) {
      this.usernameCriteriaFailed = false;
    } else {
      this.usernameCriteriaFailed = true;
    }
  }

  reset(){
    this.resetSuccess = true;
        this.resetFailed = false;
        this.passwordResetFormGroup.get('userName').reset();
        this.passwordResetFormGroup.get('password').reset();
        this.passwordResetFormGroup.get('confirmPassword').reset();
  }

}
