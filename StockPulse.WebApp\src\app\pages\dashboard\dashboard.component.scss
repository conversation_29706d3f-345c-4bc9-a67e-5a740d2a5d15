.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(5, [col-start] 1fr);
  grid-auto-rows: 1fr;
  gap: 1em;
  height: 100%;

  .dashboard-tile {
    position: relative;
    background-color: #ffffff;
    padding: 1em;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .tile-header {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid var(--grey80);
      padding-bottom: 0.5em;

      button {
        padding: 0.25em 0.5em;
      }
    }

    .tile-body {
      padding-top: 0.5em;
      flex: 1;
    }
  }
}

@for $i from 1 through 12 {
  @for $x from 1 through 12 {
    // Define columns
    .grid-col-#{$i}-#{$x} {
      grid-column-start: $i;
      grid-column-end: $x;
    }

    // Define rows
    .grid-row-#{$i}-#{$x} {
      grid-row-start: $i;
      grid-row-end: $x;
    }
  }
}

#excelDownloadButton {
  background-size: cover;
}