import { Component, OnInit } from '@angular/core';
import { Subscription } from 'dexie';
import { ItemToOpen } from 'src/app/model/ItemToOpen';
import { PhotoMapItem } from 'src/app/model/PhotoMapItem';
import { PhotoMapReportType } from 'src/app/model/PhotoMapReportType';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { PhotoMapService } from './photoMap.service';



@Component({
    selector: 'app-photoMap',
    templateUrl: './photoMap.component.html',
    styleUrls: ['./photoMap.component.scss'],
    standalone: false
})
export class PhotoMapComponent implements OnInit {

  //dataLoadedAndFormattedSubscription: Subscription;
  pageRefreshSubscription: Subscription;

  constructor(
    public service: PhotoMapService,
    public selections: SelectionsService,
    public toastService: ToastService,
    public constants: ConstantsService
  ) { }

  ngOnInit(): void {
    this.service.initParams();

    this.pageRefreshSubscription = this.constants.refreshPage.subscribe((res) => {
      if (res) { this.service.initParams(); }
    })
  }

  ngOnDestroy() {
    if (this.pageRefreshSubscription) this.pageRefreshSubscription.unsubscribe();
    this.service.chosenScanner = null;
  }

  keyValues(){
    return ['Duplicate','MatchedToStockOrScan','MatchedToOtherSite','MatchedToReport','Resolved','OutstandingIssue']
  }
 

  reportTypesArray(): string[] {
    const res: string[] = []
    Object.keys(PhotoMapReportType).map(x => {
      if (isNaN(parseInt(x))) {
        res.push(x)
      }
    })
    return res;
  }


  vehicleStateLabel(item:string){
    const scanReports = [PhotoMapReportType.allScans,PhotoMapReportType.unknownsByLocation,PhotoMapReportType.unknownsByResolutionType,PhotoMapReportType.allScansByScanner];
    const amLookingAtScans = scanReports.includes(this.service.chosenReportType);

    if(item==='Duplicate'){return `Duplicate ${amLookingAtScans ? 'scan' : 'stock item'}`}
    if(item === 'MatchedToOtherSite'){return `Matched to other site's ${amLookingAtScans ? 'stock items' : 'scans'}` }
    if(item === 'MatchedToStockOrScan'){return `Matched to ${amLookingAtScans ? 'stock':'scan'}`}
    if(item === 'MatchedToReport'){return 'Matched to report'}
    if(item === 'Resolved'){return `${amLookingAtScans?'Unknown':'Missing'} item resolved`}
    if(item === 'OutstandingIssue'){return `${amLookingAtScans?'Unknown':'Missing'} item not resolved`}
  }

  isChosenReportType(type: string) {
    return PhotoMapReportType[type] === this.service.chosenReportType;
  }

  chooseReportType(type: string) {
    this.service.chosenReportType = PhotoMapReportType[type];
    this.service.getData();
  }
  


  openVehicleModal(item: PhotoMapItem, items: PhotoMapItem[]) {
    let allIdsInGroup: ItemToOpen[] = items.map(x => ({
      id: x.id,
      isStockItem: !x.isScan
    }));
    this.constants.vehicleModal.loadItemAndOpenModal(!item.isScan, item.id, allIdsInGroup);
  }


  toggleShowPhotos() {
    this.service.showPhotosOnEachItem = !this.service.showPhotosOnEachItem
  }

  blobHolderClass(item: PhotoMapItem) {
    //if(vehicle.StockItem)
    let classes = []
    if (this.service.showPhotosOnEachItem) { classes.push('showImage') }
    classes.push(ReconciliationState[item.reconciliationState])
    return classes.join(' ')
  }


  displayTheShowPhotosSlider(){
    if(this.service.chosenReportType === PhotoMapReportType.allScans){return true;}
    if(this.service.chosenReportType === PhotoMapReportType.unknownsByLocation){return true;}
    if(this.service.chosenReportType === PhotoMapReportType.unknownsByResolutionType){return true;}
    if(this.service.chosenReportType === PhotoMapReportType.allScansByScanner){return true;}
    return false;
  }


  isChosenScanner(scannerName: string) {
    return scannerName === this.service.chosenScanner;
  }

  chooseScanner(scanner: any) {
    this.service.chosenScanner = scanner;
    this.service.filterData();
  }

 

}
