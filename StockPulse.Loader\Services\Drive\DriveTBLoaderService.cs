using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Repository.Database;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Loader.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services.Drive
{


   public class DriveTBLoaderService : GenericLoaderJobServiceParams
   {

      //constructor
      public DriveTBLoaderService()
      {

      }


      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "drive";
         string filePattern = "*NLPulse*.csv";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.DriveTB,
            customerFolder = "drive",
            filename = filePattern,
            importSPName = null,
            loadingTableName = "FinancialLines",
            jobName = "DriveTB",
            pulse = PulsesService.STK_DriveTB,
            fileType = FileType.csv,
            regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
            headerFailColumn = null,
            headerDefinitions = BuildHeaderDictionary(),
            errorCount = 0,
            dealerGroupId = 17,
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
         };

         return parms;
      }


      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.FinancialLine> incomingLines = new List<Model.Input.FinancialLine>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
         
         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

         int incomingProcessCount = 0;

         incomingLines = new List<Model.Input.FinancialLine>(10000);  //preset the list size (slightly quicker than growing it each time)

         int total = rowsAndHeaders.rowsAndCells.Count;

         logMessage.FailNotes = "";

         using (var db = new StockpulseContext())
         {
            IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary
                                                                              .Where(x => x.DealerGroupId == parms.dealerGroupId)
                                                                              .AsNoTracking()
                                                                              .AsEnumerable();

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

               try
               {

                  string siteName = rowCols[rowsAndHeaders.headerLookup["Description"]].ToUpper().Trim();

                  SiteDescriptionDictionary siteDictionary = siteDescriptionDictionary.Where(x => x.Description.Trim().ToUpper() == siteName && x.IsPrimarySiteId).FirstOrDefault();

                  if (siteDictionary == null)
                  {
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }

                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }

                  // Found site in dictionary - get SiteId (will go to catch if not found)
                  int siteId = siteDictionary.SiteId;

                  string accountDesc = rowCols[rowsAndHeaders.headerLookup["Description"]].Trim().ToString() + " " +
                                       rowCols[rowsAndHeaders.headerLookup["Description2"]].Trim().ToString() + " " +
                                       rowCols[rowsAndHeaders.headerLookup["Expense Code"]].Trim().ToString();

                  Model.Input.FinancialLine incomingLine = new Model.Input.FinancialLine()
                  {
                     SiteId = siteId,
                     Code = rowCols[rowsAndHeaders.headerLookup["Expense Code"]].Trim().ToString(),
                     AccountDescription = accountDesc,
                     Balance = decimal.Parse(rowCols[rowsAndHeaders.headerLookup["Balance"]].ToString()),
                     FileImportId = parms.fileImportId,
                     DealerGroupId = parms.dealerGroupId
                  };

                  incomingLines.Add(incomingLine);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         // Missing sites errors summarised 
         missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingLines.ToDataTable();
         result.Columns.Remove("Sites");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("DealerGroup");
         return result;
      }

      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
         {
             { "Cost Centre", "COST CENTRE" },
             { "Expense Code", "EXPENSE CODE" },
             { "Description", "DESCRIPTION" },
             { "Description2", "DESCRIPTION2" },
             { "Balance", "BALANCE" }
         };

         return headerDefinitions;
      }


   }
}
