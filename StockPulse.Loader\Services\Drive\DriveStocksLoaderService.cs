﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Repository.Database;
using Microsoft.IdentityModel.Tokens;

namespace StockPulse.Loader.Services
{

   public class DriveStocksLoaderService : GenericLoaderJobServiceParams
   {

      public DriveStocksLoaderService()
      {

      }

      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "drive";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.DriveStockItems,
            customerFolder = "drive",
            filename = "*DriveStock.csv",
            importSPName = null,
            loadingTableName = "StockItems",
            jobName = "DriveStocks",
            pulse = PulsesService.STK_DriveStock,
            fileType = FileType.csv,
            regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
            headerFailColumn = "Branch",
            headerDefinitions = BuildHeaderDictionary(),
            errorCount = 0,
            dealerGroupId = 17,
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), $"*DriveStock.csv"),
         };


         return parms;
      }

      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.StockItem> incomingStocks = new List<Model.Input.StockItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
         int incomingProcessCount = 0;
         incomingStocks = new List<Model.Input.StockItem>(10000); ;  //preset the list size (slightly quicker than growing it each time)

         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();
         Dictionary<string, int> skippedRowColsDictionary = new Dictionary<string, int>();

         using (var db = new StockpulseContext())
         {
            // Only get sites that are primary sites
            var siteDescriptionDictionary = db.SiteDescriptionDictionary
                                              .Where(x => x.IsPrimarySiteId && x.DealerGroupId == parms.dealerGroupId)
                                              .AsNoTracking()
                                              .ToList();

            var siteDictionaryLookup = siteDescriptionDictionary.ToLookup(x => x.Description);

            // Main sites
            List<Site> sites = db.Sites
                .AsNoTracking()
                .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId)
                .Include(s => s.Divisions)
                .ToList();

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               try
               {
                  if (rowCols.Length != rowsAndHeaders.headerLookup.Count())
                  {
                     //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                     string error = $"{rowCols[rowsAndHeaders.headerLookup[parms.headerFailColumn]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {rowsAndHeaders.headerLookup.Count()}";

                     if (!skippedRowColsDictionary.ContainsKey(error))
                     {
                        skippedRowColsDictionary[error] = 1;
                     }
                     else
                     {
                        skippedRowColsDictionary[error] += 1;
                     }

                     parms.errorCount++;

                     continue;
                  }

                  string siteName = rowCols[rowsAndHeaders.headerLookup["Branch"]].ToUpper().Trim();

                  Site site = sites.Where(x => x.Description.ToUpper() == siteName).FirstOrDefault();

                  int siteId;

                  if (site == null)
                  {
                     // Use the lookup for fast lookups
                     var siteDictionary = siteDictionaryLookup
                         .FirstOrDefault(g => g.Key.ToUpper() == siteName.ToUpper())?
                         .FirstOrDefault();

                     if (siteDictionary == null)
                     {
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }

                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }
                     else
                     {
                        siteId = siteDictionary.SiteId;
                     }
                  }
                  else
                  {
                     siteId = site.Id;
                  }

                  var reg = GetValue(rowCols, rowsAndHeaders, "Reg");
                  var vin = GetValue(rowCols, rowsAndHeaders, "VIN");
                  var description = GetValue(rowCols, rowsAndHeaders, "Description");
                  var branch = GetValue(rowCols, rowsAndHeaders, "Branch");
                  var stockValue = GetDecimalValue(rowCols, rowsAndHeaders, "Stock Value");
                  var dis = GetNullableIntValue(rowCols, rowsAndHeaders, "Days in stock");
                  var reference = GetValue(rowCols, rowsAndHeaders, "Stock Number");
                  
                  var dealerGroupId = parms.dealerGroupId;
                  var fileImportId = parms.fileImportId;
                  const int sourceReportId = 1;

                  // If no VIN and no reg, but we have reference - use Reference as VIN
                  if (vin.IsNullOrEmpty() && reg.IsNullOrEmpty() && !reference.IsNullOrEmpty())
                  {
                     vin = reference;
                  }

                  // If no reg or vin or reference, set VIN to be row count
                  if (reg.IsNullOrEmpty() && vin.IsNullOrEmpty() && reference.IsNullOrEmpty())
                  {
                     vin = (incomingProcessCount + 1).ToString();
                  }

                  // Now create the object
                  Model.Input.StockItem driveStock = new Model.Input.StockItem()
                  {
                     SiteId = siteId,
                     Reg = reg?.Replace(" ", "") ?? "",
                     Vin = vin,
                     Description = description,
                     DIS = dis,
                     GroupDIS = dis,
                     Comment = "",
                     StockType = "",
                     Reference = reference,
                     StockValue = stockValue,
                     DealerGroupId = dealerGroupId,
                     Branch = branch,
                     FileImportId = fileImportId,
                     SourceReportId = sourceReportId
                  };

                  incomingStocks.Add(driveStock);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         // Skipped row errors summarised
         skippedRowColsDictionary = skippedRowColsDictionary
            .OrderBy(kvp => kvp.Key) 
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in skippedRowColsDictionary)
         {
            logMessage.FailNotes += $"InterpretFile: {item.Key} ({item.Value}) <br>";
         }

         // Missing sites errors summarised 
         missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingStocks.ToDataTable();

         result.Columns.Remove("SourceReports");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("Sites");
         result.Columns.Remove("DealerGroup");

         return result;
      }

      // ------------------ Helper Methods ------------------
      private string GetValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         return rowsAndHeaders.headerLookup.ContainsKey(key)
             ? rowCols[rowsAndHeaders.headerLookup[key]]
             : null;
      }

      private int? GetNullableIntValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return null;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return int.TryParse(value, out var result) ? result : (int?)null;
      }

      private decimal GetDecimalValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return 0m;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return decimal.TryParse(value, out var result) ? result : 0m;
      }


      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
         {
             { "Reg", "REG" },
             { "VIN", "VIN" },
             { "Stock Value", "STOCK VALUE" },
             { "Stock Number", "STOCK NUMBER" },
             { "Description", "DESCRIPTION" },
             { "Days in stock", "DAYS IN STOCK" },
             { "Colour", "COLOUR" },
             { "Branch Code", "BRANCH CODE" },
             { "Branch", "BRANCH" },
             { "Account Status", "ACCOUNT STATUS" },
             { "New or Used Vehicle", "NEW OR USED VEHICLE" },
             { "Cost to Post", "COST TO POST" },
         };

         return headerDefinitions;
      }

   }
}
