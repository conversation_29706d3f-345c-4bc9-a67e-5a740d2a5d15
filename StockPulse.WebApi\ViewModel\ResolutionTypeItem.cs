﻿using System;
using System.Collections.Generic;
using System.Text.Json;

namespace StockPulse.WebApi.ViewModel
{
    //public class ResolutionTypeItem
    //{
    //    public int StockCheckId { get; set; }
    //    public string Site { get; set; }
    //    public string Division { get; set; }
    //    public DateTime StockCheckDate { get; set; }
    //    public string Status { get; set; }
    //    public string CompletedBy { get; set; }
    //    public int InStock { get; set; }
    //    public string MissingResolutionReports { get; set; }
    //    public string UnknownResolutionReports { get; set; }
    //    public List<ResolutionReport> MissingResolutions
    //    {
    //        get => string.IsNullOrEmpty(MissingResolutionReports)
    //            ? new List<ResolutionReport>()
    //            : JsonSerializer.Deserialize<List<ResolutionReport>>(MissingResolutionReports);
    //    }

    //    public List<ResolutionReport> UnknownResolutions
    //    {
    //        get => string.IsNullOrEmpty(UnknownResolutionReports)
    //            ? new List<ResolutionReport>()
    //            : JsonSerializer.Deserialize<List<ResolutionReport>>(UnknownResolutionReports);
    //    }
    //    public int Scans { get; set; }
    //}

    //public class ResolutionReport
    //{
    //    public string Description { get; set; }
    //    public int UsageCount { get; set; }
    //}

   public class ResolutionTypeItem
   {
      public int StockCheckId { get; set; }
      public string Site { get; set; }
      public string Division { get; set; }
      public DateTime StockCheckDate { get; set; }
      public string Status { get; set; }
      public string CompletedBy { get; set; }
      public int InStock { get; set; }
      public int InStockAndScanned { get; set; }
      public int Scans { get; set; }
      public int DuplicateStockItems { get; set; }
      public int DuplicateScans { get; set; }
      public List<ResolutionReportVM> MissingResolutions { get; set; } = new();
      public List<ResolutionReportVM> UnknownResolutions { get; set; } = new();
      public List<ResolutionReportVM> MissingAutoResolutions { get; set; } = new();
      public List<ResolutionReportVM> UnknownAutoResolutions { get; set; } = new();
    }  
   


   public class ResolutionReportVM
   {
      public int StockCheckId { get; set; }
      public string Description { get; set; }
      public int UsageCount { get; set; }
   }
}
