import { Component, ElementRef, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription, forkJoin } from 'rxjs';
import { ReconcilingItemTypeStat } from 'src/app/model/ReconcilingItemTypeStat';
import { LoadItemsService } from 'src/app/pages/loadItems/loadItems.service';
import { LoadItemsExcelExportService } from 'src/app/pages/loadItems/loadItemsExcelExport.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { GlobalParam } from "../../model/GlobalParam";
import { ReconcilingItemVM } from "../../model/ReconcilingItemVM";
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
import { FinancialLineVM } from 'src/app/model/FinancialLineVM';
import { ReviewImportMapsModal } from './reviewImportMapsModal/reviewImportMapsModal.component';
import { ReconcilingReportBackupComponent } from 'src/app/components/reconcilingReportBackup/reconcilingReportBackup.component';

@Component({
    selector: 'app-loadItems',
    templateUrl: './loadItems.component.html',
    styleUrls: ['./loadItems.component.scss'],
    standalone: false
})
export class ReconcilingItemsComponent implements OnInit {
  newStockItemForm: UntypedFormGroup;
  newReconcilingItemForm: UntypedFormGroup;
  reconcilingItems: ReconcilingItemTypeStat[];
  //showStock = true;
  //showTrialBalance = false;
  selectedReport: ReconcilingItemTypeStat;
  dmsRowData: ReconcilingItemVM[];
  trialBalanceRowData: ReconcilingItemVM[];
  reconcilingRowData: ReconcilingItemVM[];
  agencyRowData: ReconcilingItemVM[];

  enableImportAndDeleteButtons: boolean = false;
  subscription2: Subscription;

  filteredAgencyRowData: ReconcilingItemVM[];
  filteredDmsRowData: ReconcilingItemVM[];
  filteredTrialBalanceRowData: FinancialLineVM[];
  filteredReconcilingRowData: ReconcilingItemVM[];
  excelDownloadSubscription: Subscription;
  refreshPageSubscription: Subscription;

  @ViewChild('newVehicle', { static: true }) addVehicleModal: ElementRef;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public icon: IconService,
    public modalService: NgbModal,
    public formBuilder: UntypedFormBuilder,
    public apiAccess: ApiAccessService,
    public loadItemsService: LoadItemsService,
    public excelExport: LoadItemsExcelExportService,
    public toastService: ToastService
  ) { }

  get s() { return this.newStockItemForm.controls; }
  get r() { return this.newReconcilingItemForm.controls; }

  ngOnDestroy() {
    if (this.subscription2) this.subscription2.unsubscribe();
    if (this.excelDownloadSubscription) this.excelDownloadSubscription.unsubscribe();
    if (this.refreshPageSubscription) this.refreshPageSubscription.unsubscribe();
  }

  ngOnInit() {
    this.loadData();
    this.resetNewItemForm();
    this.initParams();

    // this.toastService.destroyToast();

    this.subscription2 = this.loadItemsService.bulkItemsLoaded.subscribe(res => {
      if (this.loadItemsService.chosenReconcilingItemType) this.chooseReconcilingItemType(this.loadItemsService.chosenReconcilingItemType);
      this.loadData();
    })

    this.excelDownloadSubscription = this.constants.loadItemsExcelDownload.subscribe(() => {
      this.generateExcel();
    })

    this.refreshPageSubscription = this.constants.refreshPage.subscribe(() => {
      this.loadData();
      this.resetNewItemForm();
      this.initParams();
    })
  }

  loadData() {
    let requests = [];
    requests.push(this.apiAccess.get('reconcilingitems', `reconcilingitemtypestats`, [{ key: 'stockcheckid', value: this.selections.stockCheck.id }]));
    requests.push(this.apiAccess.get('stockitems', 'totalitems', [{ key: 'stockcheckid', value: this.selections.stockCheck.id,  }]));
    requests.push(this.apiAccess.get('financiallines\\TotalItems', this.selections.stockCheck.id.toString()));

    if(this.constants.allowAgencyStockUpload)
    {
      requests.push(this.apiAccess.get('stockitems', 'agencystock', [{ key: 'stockcheckid', value: this.selections.stockCheck.id }]));
    }

    forkJoin(requests).subscribe((data: any) => {

      this.setReconcilingItems(data[0]);

      this.reconcilingItems.unshift(
        {
          description: 'DMS Stock', vehicleCount: data[1], id: null, explainsMissing: false, sortOrder: 0
        }, 
        {
          description: 'Trial Balance', vehicleCount: data[2], id: null, explainsMissing: false, sortOrder: 2
        }, 
      )

      if(this.constants.allowAgencyStockUpload)
      {
        this.reconcilingItems.unshift(
          {
            description: 'Agency Stock', vehicleCount: data[3], id: null, explainsMissing: false, sortOrder: 1
          }, 
        )
      }

      if (this.loadItemsService.chosenReconcilingItemType || this.loadItemsService.chosenReconcilingItemTypeDescription) {
        const description: string = this.loadItemsService.chosenReconcilingItemTypeDescription ?? this.loadItemsService.chosenReconcilingItemType.description;
        this.loadItemsService.chosenReconcilingItemType = this.reconcilingItems.find(r => r.description === description);
        this.selectedReport = this.loadItemsService.chosenReconcilingItemType;

        this.chooseReconcilingItemType(this.selectedReport)
      }

      this.toastService.destroyToast();
    });
  }

  setReconcilingItems(data: ReconcilingItemTypeStat[]) {
    data.map(x => {
      // x.explainsMissing = x.description.includes('In stock, ') ? true : false
      x.description = x.description.replace('Scanned, ', 'Explanations for Unknown Vehicles: ')
      x.description = x.description.replace('In stock, ', 'Explanations for Missing Vehicles: ')
    })
    this.reconcilingItems = data.filter(x => x.isActive).sort((a, b) => a.sortOrder - b.sortOrder);
  }

  getSpecificReconcilingItems(type:string){
    if(type==='agencyStock'){return [this.reconcilingItems.find(x=>x.description==='Agency Stock')]}
    if(type==='dmsStock'){return [this.reconcilingItems.find(x=>x.description==='DMS Stock')]}
    if(type==='trialBalance'){return [this.reconcilingItems.find(x=>x.description==='Trial Balance')]}
    if(type==='missing'){return this.reconcilingItems.filter(x=>x.description!=='Trial Balance' && x.description!== 'DMS Stock' && x.description!== 'Agency Stock' && x.explainsMissing).sort((a, b) => a.description.localeCompare(b.description));}
    if(type==='unknown'){return this.reconcilingItems.filter(x=>x.description!=='Trial Balance' && x.description!== 'DMS Stock' && x.description!== 'Agency Stock' && !x.explainsMissing).sort((a, b) => a.description.localeCompare(b.description));}
  }


  initParams() {
    
    this.loadItemsService.filter = new UntypedFormControl('');
    this.loadItemsService.chosenImportMask = null;
    this.loadItemsService.updateMainTable = new EventEmitter<boolean>();
    this.loadItemsService.importFile = null;
    this.loadItemsService.updateImportTableEmitter = new EventEmitter();
    this.loadItemsService.updateMultiSiteEmitter = new EventEmitter();
    this.loadItemsService.chosenNewHeaderLettersEmitter = new EventEmitter();
    this.loadItemsService.focusedOnHeaderLettersEmitter = new EventEmitter();
    this.loadItemsService.showImportProcess = false;

    this.chooseShowStockTable();
  }

  resetNewItemForm() {
    this.newStockItemForm = this.formBuilder.group({
      Reg: ['', [Validators.required,]],
      Vin: ['',],
      Description: ['', [Validators.required,]],
      DIS: [0, [,]],
      GroupDIS: [0,],
      Branch: ['', [Validators.required,]],
      StockType: ['', [Validators.required,]],
      Comment: ['', [Validators.required,]],
      Reference: ['', [Validators.required,]],
      StockValue: [0, [Validators.required,]],
    });
    this.newReconcilingItemForm = this.formBuilder.group({
      Reg: ['', [Validators.required,]],
      Vin: ['',],
      Description: ['', [Validators.required,]],
      Comment: ['', [Validators.required,]],
      Reference: ['', [Validators.required,]],
    });
  }

  chooseReconcilingItemType(reconcilingItem?: ReconcilingItemTypeStat) {
    this.toastService.loadingToast();

    reconcilingItem ? this.loadItemsService.chosenReconcilingItemType = reconcilingItem : null;

    this.enableImportAndDeleteButtons = !this.disableImportAndDeleteButtons();


    this.loadItemsService.chosenReconcilingItemType = this.loadItemsService.chosenReconcilingItemType;

    if (this.loadItemsService.chosenReconcilingItemType.description === 'DMS Stock') {
      this.showDmsStockTable()
    } else if (this.loadItemsService.chosenReconcilingItemType.description === 'Trial Balance') {
      this.showTrialBalanceTable()
    } else if (this.loadItemsService.chosenReconcilingItemType.description === 'Agency Stock') {
      this.showAgencyStockTable()
    } else {
      this.showReconcilingItemsTable(this.loadItemsService.chosenReconcilingItemType.id)
    };

    this.selectedReport = this.loadItemsService.chosenReconcilingItemType;
    this.loadItemsService.filter.setValue('');

    this.loadItemsService.showImportProcess = false;

    //if (this.toastService.toastReference) 
    this.loadItemsService.selectedRowsParams = null;
    this.toastService.destroyToast();
  }

  disableImportAndDeleteButtons(): boolean {
    // Disable if stock check beyond scans completed
    if (this.selections.stockCheck.statusId > 3) { return true; }
    // Disable if user role in this list
    if (!this.constants.userRolePermissions.allowDataUpload) { return true; }

    const chosenRec: string = this.loadItemsService.chosenReconcilingItemType.description;
    // If DMS or Agency, GlobalParam must be set OR SysAdmin level user to enable
    if (chosenRec === 'DMS Stock' && this.constants.IsUploadDMSStockEnabled || this.constants.userRolePermissions.alwaysAllowDataUpload) { return false; }
    if (chosenRec === 'Agency Stock' && this.constants.allowAgencyStockUpload || this.constants.userRolePermissions.alwaysAllowDataUpload) { return false; }
    // If other rec type, must not appear in GlobalParam locked rec type Ids to enable
    if (chosenRec !== 'DMS Stock' && chosenRec !== 'Agency Stock' && !this.constants.userRolePermissions.alwaysAllowDataUpload && !this.constants.lockedReconcilingItemTypeIds.includes(this.loadItemsService.chosenReconcilingItemType.id)) { return false; }
    return true;
  }

  showDmsStockTable() {
    this.apiAccess.get('stockitems', `GetDMSStockItems?stockcheckId=${this.selections.stockCheck.id}`).subscribe(data => {
      this.dmsRowData = data;
      this.selectedReport.vehicleCount = data.length;
      this.setFilteredDmsRowData(data);
    });
  }


  showAgencyStockTable() {
    this.apiAccess.get('stockitems', `GetAgencyStockItems?stockcheckId=${this.selections.stockCheck.id}`).subscribe(data => {
      this.agencyRowData = data;
      this.selectedReport.vehicleCount = data.length;
      this.setFilteredAgencyRowData(data);
      this.toastService.destroyToast();
    });
  }

  
  showTrialBalanceTable() {
    this.apiAccess.get('financiallines', `${this.selections.stockCheck.id}`).subscribe(data => {
      this.trialBalanceRowData = data;
      this.selectedReport.vehicleCount = data.length;
      this.setFilteredTrialBalanceRowData(data);
    });
  }

  showReconcilingItemsTable(recItemId) {
    this.apiAccess.get('reconcilingitems', `ReconcilingItemArray?stockcheckId=${this.selections.stockCheck.id}&reconcilingItemTypeId=${recItemId}`).subscribe(data => {
      this.reconcilingRowData = data;
      this.selectedReport.vehicleCount = data.length;
      this.reconcilingRowData.map(item => {
        if (item.scanId) {
          const strings = this.constants.buildImageStrings(item.scanId, false)
          item.regImageLargeUrl = strings.regImageLargeUrl;
          item.regImageThumbnailUrl = strings.regImageThumbnailUrl;
        }
      })
      this.setFilteredReconcilingRowData(data);
      this.loadItemsService.updateMainTable.emit();
      this.toastService.destroyToast();
    });
  }

  chooseShowStockTable() {
    this.loadItemsService.filter.setValue('');
  }

  toggleShowImportProcess() {
    this.loadItemsService.importFile = null; //reset
    this.loadItemsService.chosenImportMask = null; // reset
    this.loadItemsService.showImportProcess = true;
  }





  deleteItem(params) {

    let vehicleRegOrVin: string = params.stockItem.reg ? params.stockItem.reg : params.stockItem.vin;

    this.constants.alertModal.title = 'Really delete ' + vehicleRegOrVin + '?';
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' deletions.
      this.toastService.loadingToast('Deleting...')

      let stockItemId: number = params.stockItem.id ? params.stockItem.id : params.stockItem.stockItemId;

      this.apiAccess.get(params.controller, `Delete${params.route}?stockcheckId=${this.selections.stockCheck.id}&itemId=${stockItemId}`).subscribe(data => {
        this.loadData();
        this.chooseReconcilingItemType(this.loadItemsService.chosenReconcilingItemType);
        this.toastService.successToast('Successfully deleted item');
      });

    }, (reason) => {
      // Have chosen to cancel. 
      return
    });
  }

  deleteItemTB(params) {

    this.constants.alertModal.title = 'Really delete this financial line ?';
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' deletions.
      this.toastService.loadingToast('Deleting...');

      this.apiAccess.delete('financiallines','financialline',params.financialLine.id).subscribe(res=>{
        //ok it is gone
        this.loadData();
        this.chooseReconcilingItemType(this.loadItemsService.chosenReconcilingItemType);
        this.toastService.successToast('Successfully deleted item');
      })

    }, (reason) => {
      // Have chosen to cancel. 
      return
    });
  }

  deleteAllItems() {

    let vehicleDescriptionShort: string;
    let controlerName: string;
    let methodName: string;

    if (this.loadItemsService.chosenReconcilingItemType.description === 'DMS Stock') {
      vehicleDescriptionShort = ' stock items'
      controlerName = 'StockItems'
      methodName = `DeleteAllDMSStockItems?stockCheckId=${this.selections.stockCheck.id}`
    } else if (this.loadItemsService.chosenReconcilingItemType.description === 'Agency Stock') {
      vehicleDescriptionShort = ' stock items'
      controlerName = 'StockItems'
      methodName = `DeleteAllAgencyStockItems?stockCheckId=${this.selections.stockCheck.id}`
    }
    else if (this.loadItemsService.chosenReconcilingItemType.description === 'Trial Balance') {
      vehicleDescriptionShort = ''
      controlerName = 'FinancialLines'
      methodName = `FinancialLine/All?stockCheckId=${this.selections.stockCheck.id}`
    }
    else {
      vehicleDescriptionShort = this.loadItemsService.chosenReconcilingItemType.description.split(':')[1];
      controlerName = 'ReconcilingItems';
      methodName = `DeleteAllItems?stockcheckId=${this.selections.stockCheck.id}&reconcilingItemType=${this.loadItemsService.chosenReconcilingItemType.id}`;
    }


    this.constants.alertModal.title = 'Really delete all' + vehicleDescriptionShort + '?';
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' deletions.
      this.toastService.loadingToast('Deleting...');

      this.apiAccess.get(controlerName, methodName).subscribe(data => {
        this.loadData();
        this.reload();
        //this.chooseReconcilingItemType(this.selectedReconcilingItem);
        this.toastService.successToast('Successfully deleted items');
      });

    }, (reason) => {
      // Have chosen to cancel. 
      return
    });
  }

  reload() {
    this.toastService.loadingToast();
    // Reload the page displaying the DMS Stock table.
    // this.chooseReconcilingItemType(this.reconcilingItems[0]);
  }

  setFilteredAgencyRowData(agencyRowData: ReconcilingItemVM[]) {
    this.filteredAgencyRowData = agencyRowData;
  }

  setFilteredDmsRowData(dmsRowData: ReconcilingItemVM[]) {
    this.filteredDmsRowData = dmsRowData;
  }

  setFilteredTrialBalanceRowData(trialBalanceRowData: FinancialLineVM[]) {
    this.filteredTrialBalanceRowData = trialBalanceRowData;
  }

  setFilteredReconcilingRowData(reconcilingRowData: ReconcilingItemVM[]) {
    this.filteredReconcilingRowData = reconcilingRowData;
  }

  generateExcel() {
    this.toastService.loadingToast('Generating Excel file...');

    if (this.loadItemsService.chosenReconcilingItemType.description.includes('Agency Stock')) 
    {  
      this.excelExport.generateDmsExcelSheet(this.filteredAgencyRowData, 'Agency Stock');
    } 
    else if (this.loadItemsService.chosenReconcilingItemType.description.includes('DMS Stock')) 
    {  
      this.excelExport.generateDmsExcelSheet(this.filteredDmsRowData, 'DMS Stock');
    } 
    else if (this.loadItemsService.chosenReconcilingItemType.description.includes('Trial Balance')) 
    {
      this.excelExport.generateTrialBalanceSheet(this.filteredTrialBalanceRowData);
    }
    else 
    {
      this.excelExport.generateReconcilingExcelSheet(this.filteredReconcilingRowData, this.selectedReport.explainsMissing);
    }
  }

  selectedReportMessage() {
    if (this.enableImportAndDeleteButtons) {
      return `Showing data loaded for "${this.selectedReport.description}".   Choose 'Import from File' to add more data or 'Delete all' to remove.`
    } else {
      return `Showing data loaded for "${this.selectedReport.description}".   This data has been centrally loaded and cannot be changed.`
    }

  }

  deleteSelectedItems() {
    const params = this.loadItemsService.selectedRowsParams;

    this.constants.alertModal.title = `Really delete ${params.ids.length} rows?`;

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.toastService.loadingToast('Deleting...');

      if (params.controller === 'financiallines') {
        this.deleteSelectedItemsTB(params);
      } else {
        this.deleteSelectedItemsStockOrRec(params);
      }

    }, (reason) => {
      return;
    });
  }

  deleteSelectedItemsStockOrRec(params: any) {
    let copyOfIds: number[] = this.constants.clone(params.ids);

    params.ids.forEach(id => {
      this.apiAccess.get(params.controller, `Delete${params.route}?stockcheckId=${this.selections.stockCheck.id}&itemId=${id}`).subscribe(data => {
        copyOfIds = copyOfIds.filter(x => x !== id);
        
        if (copyOfIds.length === 0) {
          this.toastService.successToast('Successfully deleted items');
          this.loadData();
          this.chooseReconcilingItemType(this.loadItemsService.chosenReconcilingItemType);
        }
      });
    })
  }

  deleteSelectedItemsTB(params: any) {
    let copyOfIds: number[] = this.constants.clone(params.ids);
    
    params.ids.forEach(id => {
      this.apiAccess.delete('financiallines', 'financialline', id).subscribe(data => {
        copyOfIds = copyOfIds.filter(x => x !== id);
        
        if (copyOfIds.length === 0) {
          this.toastService.successToast('Successfully deleted items');
          this.loadData();
          this.chooseReconcilingItemType(this.loadItemsService.chosenReconcilingItemType);
          this.loadItemsService.selectedRowsParams = null;
        }
      });
    })
  }

  openReviewImportMapsModal() {
    const modalRef = this.modalService.open(ReviewImportMapsModal);
    modalRef.result.then(res => {
      // Do something with result
    })
  }

  openReconcilingReportBackupModal() {
    const modalRef = this.modalService.open(ReconcilingReportBackupComponent, { size: 'sm' });
    modalRef.componentInstance.stockCheckId = this.selections.stockCheck.id;
    modalRef.componentInstance.reconcilingItemTypeId = this.loadItemsService.chosenReconcilingItemType.id;
    modalRef.result.then(res => {});
  }

  allowTrialBalanceUpload() {
    return this.constants.GlobalParams?.find(x => x.name === 'allowTrialBalanceUpload')?.boolValue || this.constants.userRolePermissions.allowDataUpload;
  }
}
