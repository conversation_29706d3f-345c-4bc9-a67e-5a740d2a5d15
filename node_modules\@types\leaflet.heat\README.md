# Installation
> `npm install --save @types/leaflet.heat`

# Summary
This package contains type definitions for leaflet.heat (https://github.com/Leaflet/Leaflet.heat).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet.heat.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet.heat/index.d.ts)
````ts
import * as L from "leaflet";

declare module "leaflet" {
    type HeatLatLngTuple = [number, number, number];

    interface ColorGradientConfig {
        [key: number]: string;
    }

    interface HeatMapOptions {
        minOpacity?: number | undefined;
        maxZoom?: number | undefined;
        max?: number | undefined;
        radius?: number | undefined;
        blur?: number | undefined;
        gradient?: ColorGradientConfig | undefined;
    }

    interface HeatLayer extends TileLayer {
        setOptions(options: HeatMapOptions): HeatLayer;
        addLatLng(latlng: LatLng | HeatLatLngTuple): HeatLayer;
        setLatLngs(latlngs: Array<LatLng | HeatLatLngTuple>): HeatLayer;
    }

    function heatLayer(latlngs: Array<LatLng | HeatLatLngTuple>, options: HeatMapOptions): HeatLayer;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:38 GMT
 * Dependencies: [@types/leaflet](https://npmjs.com/package/@types/leaflet)

# Credits
These definitions were written by [Önder Ceylan](https://github.com/onderceylan).
