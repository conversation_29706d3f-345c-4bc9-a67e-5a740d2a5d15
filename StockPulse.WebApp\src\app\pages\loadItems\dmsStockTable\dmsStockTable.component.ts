import { Component, OnInit, Output, Input, EventEmitter, ViewChild, ElementRef, HostListener } from "@angular/core";
import { RegPlateComponent } from "../../../_cellRenderers/regPlate.component";
import { ChassisComponent } from "../../../_cellRenderers/chassis.component";
import { DeleteButtonComponent } from '../../../_cellRenderers/deleteButton.component';
import { ReconcilingItemVM } from "../../../model/ReconcilingItemVM";
import { ConstantsService } from '../../../services/constants.service';
import { CphPipe } from '../../../cph.pipe';
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { Subscription } from 'rxjs';
import { LoadItemsService } from "src/app/pages/loadItems/loadItems.service";
import { StockItem } from "src/app/model/StockItem";
import { LogoService } from "src/app/services/logo.service";
import { GridOptions, SelectionChangedEvent } from "ag-grid-community";




@Component({
    selector: 'dmsStockTable',
    templateUrl: './dmsStockTable.component.html',
    styleUrls: ['./dmsStockTable.component.scss'],
    standalone: false
})


export class DmsStockTableComponent implements OnInit {
  @ViewChild('tableContainer', { static: true }) tableContainer: ElementRef;
  @Output() deleteItem = new EventEmitter<{ stockItem: StockItem, controller: string, route: string }>();
  @Input() rowData: ReconcilingItemVM[];
  @Input() persistentRowData: ReconcilingItemVM[];

  @Output() filteredRowData = new EventEmitter<ReconcilingItemVM[]>();

  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  public gridApi;
  subscription: Subscription;
  sidenavToggledSubscription: Subscription;

  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public loadItemsService: LoadItemsService,
    public logo: LogoService,
    public service: LoadItemsService
  ) { }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  ngOnInit(): void {
    this.initParams()
  }

  initParams() {
    this.loadItemsService.filter.valueChanges.subscribe(result => this.search(result));
    this.subscription = this.loadItemsService.updateMainTable.subscribe(res => {
      this.search(this.loadItemsService.filter.value)
      this.updateGrid()
    })

    this.setColumnDefinitions()

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  setColumnDefinitions() {
    let gridScaleValue = this.tableContainer.nativeElement.clientWidth / 2150; //actual measurement 1310.  Added 40 for padding.

    this.mainTableGridOptions = {
      rowData: [],
      components: { agColumnHeader: CustomHeaderComponent },
      columnTypes: {
        "numberColumn": { cellClass: 'agAlignRight', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'number', 0) } },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', sortable: true },
        "currencyColumn": { cellClass: 'agAlignRight', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 2) } },
        "date": { cellClass: 'agAlignCentre', cellRenderer:(params)=>this.cphPipe.transform(params.value,'date',0), filter: 'agTextColumnFilter' }
      },
      getRowHeight: (params) => 20,
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        sortable: true,
        resizable: true,
        
        floatingFilter: true
      },
      rowSelection: 'multiple',
      enableCellTextSelection: true,
      onSelectionChanged: (event) => this.onSelectionChanged(event),
      onFirstDataRendered: (event) => this.gridApi?.sizeColumnsToFit(),
      columnDefs: [
        { headerName: "", field: "index", cellClass: 'indexCell', valueGetter: (params) => params.node.rowIndex + 1 + ' >', autoHeight: true, width: 60 * gridScaleValue },
        { headerName: "Reg", field: "reg", width: 200 * gridScaleValue, cellRenderer: RegPlateComponent, autoHeight: true, type: 'labelColumn', hide: this.constants.currencySymbol === "$" },
        { headerName: "VIN", field: "vin", width: 200 * gridScaleValue, cellRenderer: ChassisComponent, type: 'labelColumn' }, 
        { headerName: "Description", field: "description", width: 600 * gridScaleValue, type: "labelColumn" },
        { headerName: "DIS", field: "dis", width: 100 * gridScaleValue, type: 'numberColumn' },
        { headerName: "Group DIS", field: "groupDIS", width: 100 * gridScaleValue, type: 'numberColumn' },
        { headerName: "Branch", field: "branch", width: 100 * gridScaleValue, type: 'labelColumn' },
        { headerName: "Stock Type", field: "stockType", width: 200 * gridScaleValue, type: 'labelColumn' },
        { headerName: "Notes", field: "comment", width: 200 * gridScaleValue, type: 'labelColumn' },
        { headerName: "Reference", field: "reference", width: 200 * gridScaleValue, type: 'labelColumn' },
        { headerName: "Stock Value", field: "stockValue", width: 100 * gridScaleValue, type: 'currencyColumn' },
        { headerName: "Flooring Balance", field: "flooring", width: 100 * gridScaleValue, type: 'currencyColumn', hide: this.constants.currencySymbol != "$" },
        { headerName: "File Name", field: "fileName", width: 200 * gridScaleValue, type: 'labelColumn' },
        { headerName: "File Date", field: "fileDate", width: 200 * gridScaleValue, type: 'date' },
        { headerName: "Load Date", field: "loadDate", width: 200 * gridScaleValue, type: 'date' },
        { headerName: "User Name", field: "userName", width: 200 * gridScaleValue, type: 'labelColumn' },
        { headerName: "", field: "", width: 60 * gridScaleValue, type: 'labelColumn', filter: null, cellRenderer: DeleteButtonComponent, cellRendererParams: { onClick: this.processDeleteClick.bind(this), } },
        { headerName: '', field: '', width: 60, headerCheckboxSelection: true, checkboxSelection: true }
      ]
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
  }

  processDeleteClick(stockCheckItem: StockItem) {
    this.deleteItem.next({ stockItem: stockCheckItem, controller: 'stockitems', route: 'StockItems' });
  }

  search(text: string): ReconcilingItemVM[] {
    if (!this.rowData) { return }
    if (text == '') { this.rowData = this.persistentRowData; return }

    this.rowData = this.rowData.filter(stockCheckItem => {
      const term = text.toLowerCase();
      return (
        stockCheckItem.description.toLowerCase().includes(term) ||
        (stockCheckItem.reg && stockCheckItem.reg.toLowerCase().includes(term)) ||
        (stockCheckItem.vin && stockCheckItem.vin.toLowerCase().includes(term))
      )
    });

    this.filteredRowData.next(this.rowData);
  }

  updateGrid() {
    this.search('');
    this.gridApi.refreshCells();
  }

  generateExcel() {
    this.constants.loadItemsExcelDownload.emit();
  }

  onSelectionChanged(event: SelectionChangedEvent) {
    let selectedRows: any[] = event.api.getSelectedRows();
    let selectedStockItemIds: number[] = [];

    selectedRows.forEach(selectedRow => {
      selectedStockItemIds.push(selectedRow.stockItemId);
    });

    this.service.selectedRowsParams = {
      controller: 'stockitems',
      route: 'StockItems',
      ids: selectedStockItemIds
    }
  }
}
