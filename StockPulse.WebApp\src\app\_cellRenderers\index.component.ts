import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";

// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'index-cell',
    template: `{{ index+1 }}`,
    styles: [],
    standalone: false
})
export class IndexComponent implements ICellRendererAngularComp {

    index: number = 0;

    agInit(params: any): void {
        this.index = params.rowIndex;
    }

    refresh(): boolean {
        return false;
    }
}


