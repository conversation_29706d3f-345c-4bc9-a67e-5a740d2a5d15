import { ReportNameAndCount } from "./ReportNameAndCount";


export interface DataLoadedItem {
  stockCheckId: number;
  site: string;
  division: string;
  stockCheckDate: Date;
  status: string;
  completedBy: string;
  inStock: number;
  scans: number;
  missingReports: ReportNameAndCount[];
  unknownReports: ReportNameAndCount[];
}

export interface DataLoadedItemFlat {
  stockCheckId: number;
  site: string;
  division: string;
  stockCheckDate: Date;
  status: string;
  completedBy: string;
  inStock: number;
  scans: number;
  [key: string]: any;
}
