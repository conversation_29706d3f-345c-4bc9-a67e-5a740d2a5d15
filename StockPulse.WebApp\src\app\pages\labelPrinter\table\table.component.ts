import { Component, HostListener, OnInit } from "@angular/core";
import { Subscription } from 'rxjs';
import { ToastService } from "src/app/services/newToast.service";
import { ChassisComponent } from "../../../_cellRenderers/chassis.component";
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { RegPlateComponent } from "../../../_cellRenderers/regPlate.component";
import { ColumnApi, GridApi, GridReadyEvent, SelectionChangedEvent } from "ag-grid-community";
import { LabelPrinterService } from "../labelPrinter.service";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
    selector: 'labelPrinterTable',
    templateUrl: './table.component.html',
    styleUrls: ['./table.component.scss'],
    standalone: false
})

export class LabelPrinterTableComponent implements OnInit {
  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.labelPrinterService.gridApi) this.labelPrinterService.gridApi.sizeColumnsToFit();
  }

  gridOptions: GridOptionsCph;
  columnApi: ColumnApi;
  rowDataUpdatedSubscription: Subscription;
  sidenavToggledSubscription: Subscription;

  constructor(
    public labelPrinterService: LabelPrinterService,
    public toastService: ToastService,
    public constants: ConstantsService
  ) {

  }

  ngOnDestroy() {
    if (this.rowDataUpdatedSubscription) this.rowDataUpdatedSubscription.unsubscribe();
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  ngOnInit(): void {
    this.initialiseGrid();

    this.rowDataUpdatedSubscription = this.labelPrinterService.rowDataUpdatedEmitter.subscribe(() => {
      if (this.labelPrinterService.gridApi) {
        this.labelPrinterService.gridApi.setRowData(this.labelPrinterService.rowData);
      }
    })

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.labelPrinterService.gridApi) {
        this.labelPrinterService.gridApi.sizeColumnsToFit();
      }
    })
  }

  initialiseGrid() {
    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      rowData: this.labelPrinterService.rowData,
      defaultColDef: {
        floatingFilter: true,
        resizable: true
      },
      columnTypes: {
        "numberColumn": { filter: 'agNumberColumnFilter' },
        "labelColumn": { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter' }
      },
      rowSelection: 'multiple',
      columnDefs: [
        //{ headerName: "Reg", field: "reg", width: 100, cellRenderer: RegPlateComponent, type: 'labelColumn' },
        { headerName: "VIN", field: "vin", width: 100, cellRenderer: ChassisComponent, type: 'labelColumn' },
        { headerName: "Description", field: "description", width: 400, type: "labelColumn" },
        { headerName: "Colour", field: "comment", width: 200, type: "labelColumn" },
        { headerName: "Reference", field: "reference", width: 100, type: "labelColumn" },
        { headerName: "Age", field: "dis", width: 75, type: "numberColumn", sort: 'asc' },
        { width: 10, checkboxSelection: true, headerCheckboxSelection: true }
      ],
      onGridReady: (event) => this.onGridReady(event),
      onSelectionChanged: (event) => this.onSelectionChanged(event),
    }
  }

  onGridReady(event: GridReadyEvent) {
    this.labelPrinterService.gridApi = event.api;
    this.columnApi = event.columnApi;
    this.labelPrinterService.gridApi.sizeColumnsToFit();
    this.toastService.destroyToast();
  }

  onSelectionChanged(event: SelectionChangedEvent) {
    this.labelPrinterService.selectedRows = event.api.getSelectedRows();
    this.labelPrinterService.gridApi.redrawRows({ rowNodes: event.api.getSelectedNodes() });
  }
}
