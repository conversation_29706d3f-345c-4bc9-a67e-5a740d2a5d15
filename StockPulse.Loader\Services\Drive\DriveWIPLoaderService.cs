﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Repository.Database;
using Microsoft.IdentityModel.Tokens;

namespace StockPulse.Loader.Services
{

   public class DriveWIPLoaderService : GenericLoaderJobServiceParams
   {

      public DriveWIPLoaderService()
      {

      }

      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "drive";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.DriveWIP,
            customerFolder = "drive",
            filename = "*PulseWIP.csv",
            importSPName = null,
            loadingTableName = "ReconcilingItems",
            jobName = "DriveWIP",
            pulse = PulsesService.STK_DriveWIP,
            fileType = FileType.csv,
            regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
            headerFailColumn = "Branch",
            headerDefinitions = BuildHeaderDictionary(),
            errorCount = 0,
            dealerGroupId = 17,
            reconcilingItemTypeIdsToInclude = "95",
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), $"*PulseWIP.csv"),
         };


         return parms;
      }

      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.ReconcilingItem> incomingLines = new List<Model.Input.ReconcilingItem>(); 
         int incomingProcessCount = 0;
         incomingLines = new List<Model.Input.ReconcilingItem>(10000); //preset the list size (slightly quicker than growing it each time)

         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();
         Dictionary<string, int> skippedRowColsDictionary = new Dictionary<string, int>();

         using (var db = new StockpulseContext())
         {
            // Only get sites that are primary sites
            var siteDescriptionDictionary = db.SiteDescriptionDictionary
                                              .Where(x => x.IsPrimarySiteId && x.DealerGroupId == parms.dealerGroupId)
                                              .AsNoTracking()
                                              .ToList();

            var siteDictionaryLookup = siteDescriptionDictionary.ToLookup(x => x.Description);

            // Main sites
            List<Site> sites = db.Sites
                .AsNoTracking()
                .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId)
                .Include(s => s.Divisions)
                .ToList();

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               try
               {
                  if (rowCols.Length != rowsAndHeaders.headerLookup.Count())
                  {
                     //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                     string error = $"{rowCols[rowsAndHeaders.headerLookup[parms.headerFailColumn]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {rowsAndHeaders.headerLookup.Count()}";

                     if (!skippedRowColsDictionary.ContainsKey(error))
                     {
                        skippedRowColsDictionary[error] = 1;
                     }
                     else
                     {
                        skippedRowColsDictionary[error] += 1;
                     }

                     parms.errorCount++;

                     continue;
                  }

                  string siteName = rowCols[rowsAndHeaders.headerLookup["Branch Name"]].ToUpper().Trim();

                  // TODO Andy Skip these until we know how to link
                  if(siteName == "DRIVE BRISTOL" || siteName == "DRIVE YATE")
                  {
                     continue;
                  }

                  Site site = sites.Where(x => x.Description.ToUpper() == siteName).FirstOrDefault();

                  int siteId;

                  if (site == null)
                  {
                     // Use the lookup for fast lookups
                     var siteDictionary = siteDictionaryLookup
                         .FirstOrDefault(g => g.Key.ToUpper() == siteName.ToUpper())?
                         .FirstOrDefault();

                     if (siteDictionary == null)
                     {
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }

                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }
                     else
                     {
                        siteId = siteDictionary.SiteId;
                     }
                  }
                  else
                  {
                     siteId = site.Id;
                  }

                  var reg = GetValue(rowCols, rowsAndHeaders, "Registration");
                  var vin = GetValue(rowCols, rowsAndHeaders, "VIN");
                  var wipNumber = GetValue(rowCols, rowsAndHeaders, "WIP Number");
                  
                  var dealerGroupId = parms.dealerGroupId;
                  var fileImportId = parms.fileImportId;
                  const int sourceReportId = 1;

                  // If no reg or vin, set VIN to be row count
                  if (reg.IsNullOrEmpty() && vin.IsNullOrEmpty())
                  {
                     vin = (incomingProcessCount + 1).ToString();
                  }

                  // Now create the object
                  Model.Input.ReconcilingItem driveItem = new Model.Input.ReconcilingItem()
                  {
                     ReconcilingItemTypeId = 95,
                     Reg = reg?.Replace(" ", "") ?? "",
                     Vin = vin,
                     Description = wipNumber,
                     Comment = "",
                     Reference = wipNumber,
                     SourceReportId = sourceReportId,
                     SiteId = siteId,
                     FileImportId = fileImportId,
                     DealerGroupId = dealerGroupId
                  };

                  incomingLines.Add(driveItem);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         // Skipped row errors summarised
         skippedRowColsDictionary = skippedRowColsDictionary
            .OrderBy(kvp => kvp.Key) 
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in skippedRowColsDictionary)
         {
            logMessage.FailNotes += $"InterpretFile: {item.Key} ({item.Value}) <br>";
         }

         // Missing sites errors summarised 
         missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingLines.ToDataTable();

         result.Columns.Remove("Sites");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("ReconcilingItemType");
         result.Columns.Remove("DealerGroup");
         result.Columns.Remove("SourceReports");

         return result;
      }

      // ------------------ Helper Methods ------------------
      private string GetValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         return rowsAndHeaders.headerLookup.ContainsKey(key)
             ? rowCols[rowsAndHeaders.headerLookup[key]]
             : null;
      }

      private int? GetNullableIntValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return null;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return int.TryParse(value, out var result) ? result : (int?)null;
      }

      private decimal GetDecimalValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return 0m;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return decimal.TryParse(value, out var result) ? result : 0m;
      }


      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
         {
             { "Registration", "REGISTRATION" },
             { "WIP Number", "WIP NUMBER" },
             { "VIN", "VIN" },
             { "WIP Created Date", "WIP CREATED DATE" },
             { "Invoice Status", "INVOICE STATUS" },
             { "Department Code", "DEPARTMENT CODE" },
             { "Branch", "BRANCH" },
             { "Branch Name", "BRANCH NAME" },
         };

         return headerDefinitions;
      }

   }
}
