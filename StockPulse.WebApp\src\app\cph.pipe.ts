import { Injector, Pipe, PipeTransform } from '@angular/core';
import { CurrencyPipe } from '@angular/common'
import { DecimalPipe } from '@angular/common'
import { DatePipe } from '@angular/common'
import { ConstantsService } from './services/constants.service';

@Pipe({
    name: 'cph',
    standalone: false
})
export class CphPipe implements PipeTransform {

    constructor(
        private currencyPipe: CurrencyPipe,
        private decimalPipe: DecimalPipe,
        private datePipe: DatePipe,
        private injector: Injector

    ) { }

    transform(value: any, pipeType:string, decimalPlaces:number,includePrefix?:boolean): any {
        //if given empty string
        if(value=='.'){return '0.'}
        if(value=='-'){return '-'}
        if(value==0){return '-'}


        let prefix = ''
        if(includePrefix){
            prefix = '';
            if(value>=0){prefix = '+'}
        }

        let chassisValue = value;
        if(pipeType ==='chassis' && (value===''||value===null)){
            chassisValue = '-'
        }

        let numberPlateValue = value;
        if(pipeType == 'numberPlate'){
            if(value &&  value.length==7){
                //test if is in the format LLNNLLL
                if(isNaN(value[0]) && isNaN(value[1]) && !isNaN(value[2]) && !isNaN(value[3]) && isNaN(value[4]) && isNaN(value[5]) && isNaN(value[6]) ){
                    numberPlateValue = value.substring(0,4) + ' ' + value.substring(4,7);
                }else{
                    numberPlateValue = value;
                }

            }
        }

        const constantsService = this.injector.get(ConstantsService);

        //various return paths
        switch (pipeType){
            case 'number':{return prefix + this.decimalPipe.transform(value,'1.'+decimalPlaces+'-'+decimalPlaces);}
            case 'numberNoPrefix':{return this.decimalPipe.transform(Math.abs(value),'1.0-0');}
            case 'date':{return this.datePipe.transform(value,'d MMM yyyy');}
            case 'dateTime':{return `${this.datePipe.transform(value,'d MMM yyyy')} ${this.datePipe.transform(value,'H:mm')}` ;}
            case 'dateTimeNoYear':{return `${this.datePipe.transform(value,'d MMM ')} ${this.datePipe.transform(value,'H:mm')}` ;}
            case 'day': {return this.datePipe.transform(value, 'EEEE dd LLLL')}
            case 'shortDate': {return this.datePipe.transform(value, 'd MMM')}
            case 'regConfidence': { return this.decimalPipe.transform(value, '1.' + decimalPlaces + '-' + decimalPlaces) + '%'  }
            case 'vinConfidence': { return this.decimalPipe.transform(value, '1.' + decimalPlaces + '-' + decimalPlaces) + '%'  }
            case 'miles': { return this.decimalPipe.transform(value , `1.${decimalPlaces}-${decimalPlaces}`) + ' miles'  }
            case 'dateMed':{return this.datePipe.transform(value,'d MMM yy');}
            case 'time':{return this.datePipe.transform(value,'shortTime');}
            case 'shortTime':{return this.datePipe.transform(value,'H:mm');}
            case 'hour':{return this.datePipe.transform(value,'H a');}
            case 'hour12':{return this.datePipe.transform(value,'h a');}
            case 'weight':{return prefix + this.decimalPipe.transform(value,'1.'+decimalPlaces+'-'+decimalPlaces)+'g';}
            case 'percent':{return prefix + this.decimalPipe.transform(value*100,'1.'+decimalPlaces+'-'+decimalPlaces)+'%'}
            case 'currency':{return prefix + this.currencyPipe.transform(value, constantsService.currency, constantsService.currencySymbol, '1.' + decimalPlaces + '-' + decimalPlaces) }
            case 'proper':{return prefix + value.substring(0,1).toUpperCase() + value.substring(1,value.length)}
            case 'numberPlate':{return numberPlateValue }
            case 'chassis':{return chassisValue}
            case 'excelDate':{return this.datePipe.transform(value,'dd/MM/yyyy');}
            case 'excelTime':{return this.datePipe.transform(value,'H:mm');}
            case 'miles':{return `${this.decimalPipe.transform(value,'1.'+decimalPlaces+'-'+decimalPlaces)}miles`;}
            case 'dayMonthTime': { return this.datePipe.transform(value, 'dd/MM HH:mm') || ''; }
            case 'splitCamelCase': { return value.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim(); }
            case 'timespan': {
                if (!value){
                    return '0hrs';
                }

                const timeParts = value.split(':');
                if (timeParts.length < 3) return '0hrs';

                const hoursParts = timeParts[0].split('.');
                const days = hoursParts.length > 1 ? parseInt(hoursParts[0], 10) : 0;
                const hours = parseInt(hoursParts[hoursParts.length - 1], 10);
                const minutes = parseInt(timeParts[1], 10);

                // Calculate total hours, rounding up if there are any minutes
                let totalHours = hours + (minutes > 0 ? 1 : 0);
                const totalDays = days + Math.floor(totalHours / 24);
                totalHours = totalHours % 24;

                let timeToReturn = "";

                // Add days if there are any
                if (totalDays > 0) {
                    timeToReturn += `${totalDays}d `;
                }

                // Add hours if there are any or if there are no days
                if (totalHours > 0 || totalDays === 0) {
                    timeToReturn += `${totalHours}hrs`;
                }

                return timeToReturn.trim();
            }
            case 'dayMonthAndTime': {
                return this.datePipe.transform(value, 'dd MMM HH:mm');
            }
            default: debugger;alert('pipeType not found! '+pipeType);
        }
    }
}

