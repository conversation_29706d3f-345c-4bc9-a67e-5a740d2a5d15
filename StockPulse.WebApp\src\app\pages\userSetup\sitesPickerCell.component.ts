import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import { SiteVM } from "src/app/model/SiteVM";
import { ConstantsService } from '../../services/constants.service';
import {IconService} from '../../services/icon.service'
import { SiteVMWithSelected } from "../../model/SiteVMWithSelected";


@Component({
    selector: 'sitesPicker-cell',
    template: `
    
    <sitePicker *ngIf="sites" [width]="250" [sitesFromParent]="sites" (updateSites)="onUpdateSites($event)"></sitePicker>
  `,
    styles: [
        `
 #mainButton{width:200px;display: flex;
    justify-content: space-between;
    align-items: center;}
`
    ],
    standalone: false
})
export class SitesPickerCellRendererComponent implements ICellRendererAngularComp {
    params;
    siteCodes:string[];
    sites:SiteVMWithSelected[];

    constructor(
      public icon: IconService,
      public constants: ConstantsService,
    ) { }


    agInit(params: any): void {
      this.params = params;
      
      if (this.params.data.newValues.sites) {
        this.siteCodes = this.params.data.newValues.sites.split(',');
      } else {
        this.siteCodes = this.params.data.sites ? this.params.data.sites.split(',') : [];
      }
      
     this.setInitialSites();
    }

    setInitialSites() {

      let sites = [];
      
      this.siteCodes.forEach(id=>{
        sites.push(this.constants.SitesActive.find(x=>x.id===parseInt(id)))
      })

      this.sites = sites.filter(x => x != null);
    }

    refresh(params?:any): boolean {
      this.agInit(params)  
      return false;
    }

    onUpdateSites(sites:SiteVM[]){
      let siteCodes:number[] = sites.map(x=>x.id)
      this.params.node.data.newValues.sites = siteCodes.join(',')
      let data = this.params.node.data;
      data.hasChanged = true;
      this.params.node.setData(data);
    }
}


