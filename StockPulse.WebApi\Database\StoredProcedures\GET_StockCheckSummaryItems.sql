CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckSummaryItems]  
(
    @UserId INT = NULL
)  
AS  
BEGIN
    SET NOCOUNT ON;

    DECLARE @DealerGroupId INT;
    SELECT TOP 1 @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

    -- Pre-aggregate MissingUnresolved counts
    ;WITH MissingCounts AS (
        SELECT 
            sti.StockCheckId,
            COUNT(*) AS MissingUnresolved
        FROM StockItems sti
        INNER JOIN StockChecks sc ON sc.Id = sti.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions d ON d.Id = s.DivisionId
        LEFT JOIN MissingResolutions mr ON mr.Id = sti.MissingResolutionId
        WHERE 
            d.DealerGroupId = @DealerGroupId AND
            sti.IsDuplicate = 0 AND
            sti.ScanId IS NULL AND 
            sti.ReconcilingItemId IS NULL AND
            (mr.Id IS NULL OR mr.IsResolved = 0)
        GROUP BY sti.StockCheckId
    ),
    -- Pre-aggregate UnknownUnresolved counts
    UnknownCounts AS (
        SELECT 
            sca.StockCheckId,
            COUNT(*) AS UnknownUnresolved
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions d ON d.Id = s.DivisionId
        LEFT JOIN UnknownResolutions ur ON ur.Id = sca.UnknownResolutionId
        WHERE 
            d.DealerGroupId = @DealerGroupId AND
            sca.IsDuplicate = 0 AND
            sca.StockItemId IS NULL AND 
            sca.ReconcilingItemId IS NULL AND
            (ur.Id IS NULL OR ur.IsResolved = 0)
        GROUP BY sca.StockCheckId
    ),
    -- Pre-aggregate total stock items
    StockItemCounts AS (
        SELECT 
            sti.StockCheckId,
            COUNT(*) AS StockItems
        FROM StockItems sti
        INNER JOIN StockChecks sc ON sc.Id = sti.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions d ON d.Id = s.DivisionId
        WHERE d.DealerGroupId = @DealerGroupId
        GROUP BY sti.StockCheckId
    ),
    -- Pre-aggregate total scans
    ScanCounts AS (
        SELECT 
            sca.StockCheckId,
            COUNT(*) AS Scans
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions d ON d.Id = s.DivisionId
        WHERE d.DealerGroupId = @DealerGroupId
        GROUP BY sca.StockCheckId
    )

    -- Final result
    SELECT 
        sc.Id,
        s.Description AS Site,
        st.Description AS Status,
        ISNULL(mc.MissingUnresolved, 0) AS MissingUnresolved,
        ISNULL(uc.UnknownUnresolved, 0) AS UnknownUnresolved,
        ISNULL(sic.StockItems, 0) AS StockItems,
        ISNULL(scans.Scans, 0) AS Scans,
		sc.Date AS StockCheckDate
    FROM StockChecks sc
    INNER JOIN Sites s ON s.Id = sc.SiteId
    INNER JOIN Divisions d ON d.Id = s.DivisionId
    INNER JOIN Statuses st ON st.Id = sc.StatusId
    LEFT JOIN MissingCounts mc ON mc.StockCheckId = sc.Id
    LEFT JOIN UnknownCounts uc ON uc.StockCheckId = sc.Id
    LEFT JOIN StockItemCounts sic ON sic.StockCheckId = sc.Id
    LEFT JOIN ScanCounts scans ON scans.StockCheckId = sc.Id
    WHERE 
        sc.IsActive = 1 AND
        d.DealerGroupId = @DealerGroupId AND
        sc.IsRegional = 0 AND
        sc.IsTotal = 0
    ORDER BY
        sc.[Date] DESC, 
        s.[Description];

END;
