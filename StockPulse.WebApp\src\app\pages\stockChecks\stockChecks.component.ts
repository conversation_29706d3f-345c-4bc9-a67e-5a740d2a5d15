import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ConstantsService } from '../../services/constants.service';
import { Ngb<PERSON>alendar, NgbDateStruct, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { IconService } from '../../services/icon.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { SiteVM } from "src/app/model/SiteVM";
import { forkJoin, Subscription } from 'rxjs';
import { StockChecksService } from './stockChecks.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { CreateAllStockCheckParams } from 'src/app/model/CreateAllStockCheckParams';
import { ImportLatestDataParams } from 'src/app/model/ImportLatestDataParams';
import { CphPipe } from 'src/app/cph.pipe';
import { StockCheck } from 'src/app/model/StockCheck';
import { SiteVMWithSelected } from 'src/app/model/SiteVMWithSelected';
import { DashboardService } from '../dashboard/dashboard.service';

@Component({
  selector: 'app-stockChecks',
  templateUrl: './stockChecks.component.html',
  styleUrls: ['./stockChecks.component.scss']
})
export class StockChecksComponent implements OnInit {

  @ViewChild('newStockCheckModal', { static: true }) newStockCheckModal: ElementRef;
  @ViewChild('newImportLatestDataModal', { static: true }) newImportLatestDataModal: ElementRef;

  newStockCheckDate: string;
  chosenSites: SiteVMWithSelected[];
  Sites: SiteVMWithSelected[] = [];
  pageRefreshSubscription: Subscription;
  allSiteModal: boolean = false;
  selectedStockCheckIds: number[] = [];

  importMessage: string;
  disableImportLatestDataButton: boolean;
  searchString: string = '';

  public tables: string[] = [
    "Overview"
  ];

  constructor(
    public constants: ConstantsService,
    public modalService: NgbModal,
    public icon: IconService,
    public apiAccess: ApiAccessService,
    public service: StockChecksService,
    public toastService: ToastService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    private dashboardService: DashboardService
  ) { }


  ngOnInit() {
    this.toastService.destroyToast();
    this.disableImportLatestDataButton = true;

    this.tables.push("Resolution Types","Data Loaded","Stock Check Quality","Stock Check Speed")

    if (!this.service.toDate) {
      this.service.toDate = new Date().toISOString().split('T')[0];

      let year: number = new Date().getFullYear();
      let month: number = new Date().getMonth() - 2;
      let day: number = new Date(year, month, 0).getDate();
     
      this.service.fromDate = new Date(year, month, day).toISOString().split('T')[0];
    }

    this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => { });

    this.apiAccess.get('Sites', 'All').subscribe((data: SiteVMWithSelected[]) => {
      this.constants.SitesActive = data.filter(x => x.isActive).sort((a, b) => a.description.localeCompare(b.description));
    });

    this.pageRefreshSubscription = this.constants.refreshPage.subscribe((res) => {
      if (res) {
        this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => {

        });
      }
    })
  }

  ngOnDestroy() {
    if (this.pageRefreshSubscription) this.pageRefreshSubscription.unsubscribe();
  }

  changeDate(event) {
    this.newStockCheckDate = event.srcElement.value
  }

  isDeleteStockCheckButtonDisabled(): boolean {

    if(this.selectedStockCheckIds.length == 0){
      return true;
    }

    if (this.service.stockCheckVMs.filter(s => this.selectedStockCheckIds.includes(s.id) && (s.scans > 0 || s.stockItemsCount > 0)).length > 0){
      return true;
    }

    return false;
  }

  showNewStockCheckModal() : void {

    this.newStockCheckDate = new Date().toISOString().split('T')[0];
    this.allSiteModal = false;

    this.modalService.open(this.newStockCheckModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections
      this.apiAccess.post('StockChecks', 'CheckIfAlreadyExists', { StockCheckDate: this.newStockCheckDate, SiteIds: this.chosenSites.map(x => x.id) }).subscribe((res: any) => {
        if (res.length > 0) {
          const matchingStockCheckSiteDescriptions: string = this.chosenSites.filter(x => res.includes(x.id)).map(x => x.description).join(', ');
          this.toastService.errorToast(`A Stock check dated ${this.cphPipe.transform(this.newStockCheckDate, 'dateMed', 0)} already exists for the following sites: ${matchingStockCheckSiteDescriptions}`);
        } else {
          this.createNewStockCheck(); 
        }
      });
    }, (reason) => {
      this.resetNewStockCheckModal();
    });

  }

  // Import new data for Stockchecks (Marshalls only)
  showImportLatestDataModal() {

    this.service.getLatestDataRecievedDate();

    this.newStockCheckDate = new Date().toISOString().split('T')[0];
    this.allSiteModal = false;

    // The message that will be displayed to the user on the modal
    this.importMessage = this.getImportMessage();

    this.modalService.open(this.newImportLatestDataModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {

      this.service.importInProgress = true;
      this.service.importInProgressSubject.next(true); 
      //have chosen to 'OK' selections
      this.importLatestData();

    }, (reason) => {

    });

  }


  shouldDisableImportDataButton()
  {

    if(this.service?.importInProgress)
    {
      this.disableImportLatestDataButton = true;
      return;
    }
    // There is at least one selected stock check
    else if(this.selectedStockCheckIds.length == 0)
    {
      this.disableImportLatestDataButton = true;
      return true;
    }
    else
    {

      let invalidStatus = false;
      let selected = this.service.stockCheckVMs.filter(x => this.selectedStockCheckIds.includes(x.id));

      // Ensure all StockChecks are Not Started / Scans In Progress
      selected.forEach(element => {

        if(element.statusId != 1 && element.statusId != 2)
        {
          invalidStatus = true;
        }
        
      });

      // None of the above conditions are met: set as active
      this.disableImportLatestDataButton = invalidStatus;
      return;
    }

  }

  getImportMessage() : string {

    let missingResolutions: number = this.getTotalMissingResolutionsForSelectedStockChecks();
    let stockItemsCount: number = this.getStockItemsCount();

    if(missingResolutions > 0)
    {
      return 'Are you sure you want to import the latest data and overwrite the current data for ' + this.selectedStockCheckIds.length + 
      ' Stock Checks? ' + missingResolutions + ' Missing Resolutions will be deleted. This cannot be undone.';
    }
    else if(stockItemsCount > 0)
    {
      return 'Are you sure you want to import the latest data and overwrite the current data for ' + this.selectedStockCheckIds.length + 
      ' Stock Checks? This cannot be undone.';

    }
    else
    {
      return 'Are you sure you want to import the latest data for ' + this.selectedStockCheckIds.length + ' Stock Checks?';

    }

  }

  getStockItemsCount() : number {
    let selected = this.service.stockCheckVMs.filter(x => this.selectedStockCheckIds.includes(x.id));

    let result = 0;

    selected.forEach(element => {
      
      result += element.stockItemsCount;

    });

    return result;
  }

  getTotalMissingResolutionsForSelectedStockChecks() : number {
    
    let selected = this.service.stockCheckVMs.filter(x => this.selectedStockCheckIds.includes(x.id));

    let result = 0;

    selected.forEach(element => {
      
      result += element.missings;

    });

    return result;
  }

  importLatestData() {

    this.shouldDisableImportDataButton();

    let parms: ImportLatestDataParams = {
      StockcheckIds: this.selectedStockCheckIds,
    }

    this.toastService.loadingToast('Loading...')

    this.apiAccess.post('StockChecks', 'ImportLatestData', parms).subscribe(() => {

      this.toastService.destroyToast();
      this.toastService.successToast('Latest data imported');

      this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => {

      });

    }, e => {
      this.toastService.destroyToast();
      this.toastService.errorToast('Failed to import');
      this.toastService.errorToast(e);

      this.service.importInProgress = false;
      this.service.importInProgressSubject.next(false); 
      this.shouldDisableImportDataButton();
    })

    this.shouldDisableImportDataButton();
  }

  showNewStockCheckModalAllSites() {

    this.newStockCheckDate = new Date().toISOString().split('T')[0];
    this.allSiteModal = true;

    this.modalService.open(this.newStockCheckModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections
      this.apiAccess.post('StockChecks', 'CheckIfAlreadyExists', { StockCheckDate: this.newStockCheckDate, SiteIds: this.Sites.map(x => x.id) }).subscribe((res: any) => {
        if (res.length > 0) {
          const matchingStockCheckSiteDescriptions: string = this.Sites.filter(x => res.includes(x.id)).map(x => x.description).join(', ');
          this.toastService.errorToast(`A Stock check dated ${this.cphPipe.transform(this.newStockCheckDate, 'dateMed', 0)} already exists for the following sites: ${matchingStockCheckSiteDescriptions}`);
        } else {
          this.createStockChecksForAllSites(); 
        }
      });
    }, (reason) => {

    });

  }

  onChosenSites(sites: SiteVMWithSelected[]) {
    this.chosenSites = sites;
  }

  createNewStockCheck() {
    let newStockCheck = { "SiteIds": this.chosenSites.map(x => x.id), "Date": this.newStockCheckDate };

    this.apiAccess.post('StockChecks', 'Create', newStockCheck).subscribe(
      (results: number[]) => {
        this.toastService.successToast('New stock check(s) created');
        this.resetNewStockCheckModal();

        this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => {
          // const newlyCreatedStockCheck = this.service.stockCheckVMs.find(x => x.id === results[0]);
          // this.service.loadStockCheck(newlyCreatedStockCheck, false)
        });
      },
      (error) => {
        // Handle error if any request fails
        console.error('An error occurred', error);
      }
    );
  }

  createStockChecksForAllSites() {


    // SiteId is ignored here
    let newStockCheck: CreateAllStockCheckParams = { Date: this.newStockCheckDate }

    this.apiAccess.post('StockChecks', 'CreateStockChecksForAllSites', newStockCheck).subscribe(() => {

      this.toastService.successToast('New stock checks created for all sites');

      this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => {

      });

    });


  }

  toggleStockchecks(active: boolean) {
    if (active === this.service.showActive) return;

    this.service.showActive = active;

    if (this.service.chosenTable === 'Overview') {
      this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => { });
    } else {
      this.changeTable(this.service.chosenTable);
    }

  }

  showNewStockCheckButton() {
    return this.service.showActive && (this.constants.GlobalParams?.find(x => x.name === 'doShowCreateNewStockCheckButton')?.boolValue || this.constants.userRolePermissions.canCreateNewStockChecks)
  }

  showNewAllSiteStockCheckButton() {
    //console.log(this.selections.userRole)
    return this.constants.userRolePermissions.canCreateNewStockChecks && this.service.showActive;
  }

  disableNewAllSiteStockCheckButton() {
    return this.service.stockCheckVMs.length >= 1;
  }

  archive() {

    if (this.selectedStockCheckIds.length > 1) this.constants.alertModal.title = 'Really archive ' + this.selectedStockCheckIds.length + ' stock checks?';
    else this.constants.alertModal.title = 'Really archive ' + this.selectedStockCheckIds.length + ' stock check?';


    this.constants.alertModal.message = '';
    this.constants.alertModal.showOkInSuccessColour = true;

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' archive.
      this.constants.alertModal.showOkInSuccessColour = false;
      this.service.archiveStockChecks(this.selectedStockCheckIds, false).subscribe(() => {
        this.selectedStockCheckIds = [];
      })

    }, (reason) => {
      // Have chosen to cancel. 
      this.constants.alertModal.showOkInSuccessColour = false;
      return
    });


  }

  unArchive() {



    if (this.selectedStockCheckIds.length > 1) this.constants.alertModal.title = 'Really set ' + this.selectedStockCheckIds.length + ' stock checks to current?';
    else this.constants.alertModal.title = 'Really set ' + this.selectedStockCheckIds.length + ' stock check to current?';

    this.constants.alertModal.showOkInSuccessColour=true;
    this.constants.alertModal.message = '';
    
    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' unarchive.
      this.constants.alertModal.showOkInSuccessColour=false;
      
      this.service.unArchiveStockChecks(this.selectedStockCheckIds, false).subscribe(() => {
        this.selectedStockCheckIds = [];
      })
    }, (reason) => {
      // Have chosen to cancel. 
      this.constants.alertModal.showOkInSuccessColour=false;
      return
    });




  }


  delete() {


    if (this.selectedStockCheckIds.length > 1) this.constants.alertModal.title = 'Really delete ' + this.selectedStockCheckIds.length + ' stock checks?';
    else this.constants.alertModal.title = 'Really delete ' + this.selectedStockCheckIds.length + ' stock check?';


    this.constants.alertModal.message = '';
    this.constants.alertModal.showOkInSuccessColour = true;

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' archive.
      this.constants.alertModal.showOkInSuccessColour = false;
      this.service.deleteStockChecks(this.selectedStockCheckIds, false).subscribe(() => {
        this.selectedStockCheckIds = [];
      })

    }, (reason) => {
      // Have chosen to cancel. 
      this.constants.alertModal.showOkInSuccessColour = false;
      return
    });


  }

  setSelectedStockChecks(stockCheckIds: number[]) {
    this.selectedStockCheckIds = stockCheckIds;
    this.shouldDisableImportDataButton();
  }

  reloadArchivedStockChecks() {
    if (this.service.chosenTable === 'Overview') {
      this.service.getAndSetStockChecks(this.service.showActive, false, this.service.fromDate, this.service.toDate).subscribe(res => { });
    } else {
      this.changeTable(this.service.chosenTable);
    }
  }

  resetNewStockCheckModal() {
    this.newStockCheckDate = new Date().toDateString();
    this.chosenSites = null;
    this.constants.SitesActive.map(x => x.isSelected = false);
  }

  searchList() {
    let sitesCopy: SiteVMWithSelected[] = JSON.parse(JSON.stringify(this.constants.Sites));
    this.Sites = sitesCopy.filter(site => site.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }

  showImportLatestDataImport() {
    // If automated data import global param is not true
    if (!this.constants.AutomatedDataImport) {
      return false;
    }

    // If a non system admin, and global param to only show to system admins is true
    if (this.constants.restrictImportDataToSysAdmins) {
      return false;
    }

    // If viewing inactive stock checks
    if (!this.service.showActive) {
      return false;
    }

    // Otherwise, show the button
    return true;
  }

  async changeTable(table: string) {
    this.toastService.loadingToast();

    switch (table) {
      case "Stock Check Quality":
        this.dashboardService.getStockCheckQualityRowData();
        break;
      case "Stock Check Speed":
        this.dashboardService.getStockCheckSpeedRowData();
        break;
      case "Resolution Types":
        this.dashboardService.resolutionTypesRowData =
          await this.dashboardService.getResolutionTypesRowData();
        this.toastService.destroyToast();
        break;
      case "Data Loaded":
        this.dashboardService.getDataLoadedRowData();
        break;
      default:
        this.toastService.destroyToast();
        break;
    }

    this.service.chosenTable = table;
  }
}
