import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { IconService } from '../services/icon.service';


@Component({
    selector: 'checkbox-cell',
    template: `
    <button class="custom-checkbox" [ngClass]="{ 'checked': params.value }"
      [disabled]="params.disabled" (click)="update()">
      <fa-icon *ngIf="params.value" [icon]="icon.faCheck"></fa-icon>
    </button>
  `,
    standalone: false
})
export class CheckboxComponent implements ICellRendererAngularComp {
  params: any;
  parent: any;
  nameField: string;

  constructor(
    public icon: IconService
  ) { }

  agInit(params: any): void {
    this.params = params;
    this.parent = params.parent;
    this.nameField = params.nameField;
  }

  refresh(params?: any): boolean {
    return false;
  }

  update() {
    this.params.value = !this.params.value;
    this.parent.updateChanges(this.params.data.id, this.params.value, this.params.data[this.nameField]);
  }
}
