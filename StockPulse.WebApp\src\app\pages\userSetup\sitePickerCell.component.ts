import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import { SiteVM } from "src/app/model/SiteVM";
import { ConstantsService } from '../../services/constants.service';
import {IconService} from '../../services/icon.service'


@Component({
    selector: 'sitePicker-cell',
    template: `
    
     <!-- Site dropdown -->
     <div ngbDropdown container="body" id="chooseNewRole" class="d-inline-block" >
                  <button class="btn btn-primary" id="mainButton" ngbDropdownToggle>
                      <span *ngIf="chosenSite">{{chosenSite.description}}</span>
                      <span *ngIf="!chosenSite">Choose Site..</span>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button (click)="chooseSite(site)" *ngFor="let site of constants.SitesActive"
                      ngbDropdownItem>{{site.description}}</button>
                  </div>
                </div>


  `,
    styles: [
        `
        #mainButton{width:200px;display: flex;
    justify-content: space-between;
    align-items: center;}
      `
    ],
    standalone: false
})
export class SitePickerCellRendererComponent implements ICellRendererAngularComp {
    params;
    chosenSite:SiteVM

    constructor(
      public icon: IconService,
      public constants: ConstantsService,
    ) { }


    agInit(params: any): void {
     this.params = params;
     this.chosenSite = this.constants.SitesActive.find(x=>x.id == this.params.data.siteCode)
    
    }

  

    refresh(params?:any): boolean {
      return false;
    }

    chooseSite(site:SiteVM){
    this.chosenSite = site;
    this.params.node.data.newValues.siteCode = site.id;
    let data = this.params.node.data;
    data.hasChanged = true;
    this.params.node.setData(data);
  }
}


