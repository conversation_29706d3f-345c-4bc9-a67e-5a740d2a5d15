﻿using Microsoft.Exchange.WebServices.Data;
using StockPulse.Model;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
   public interface IDashboardService
   {
      Task<Dashboard> GetDashboard(DashboardParams dashboardParams, int userId);
      Task<IEnumerable<StockCheckQualityItem>> GetStockCheckQualityRowData(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate);
      Task<IEnumerable<StockCheckSpeedItem>> GetStockCheckSpeedRowData(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate);
      Task<List<DataLoadedItem>> GetDataLoadedItems(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate);
      Task<List<ResolutionTypeItem>> GetResolutionTypeRows(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate);
   }

   public class DashboardService : IDashboardService
   {
      private readonly IDashboardDataAccess dashboardDataAccess;

      public DashboardService(IDashboardDataAccess dashboardDataAccess)
      {
         this.dashboardDataAccess = dashboardDataAccess;
      }

      public async Task<Dashboard> GetDashboard(DashboardParams dashboardParams, int userId)
      {
         //Summary items
         var summaryItems = await dashboardDataAccess.GetStockCheckSummaryItems(userId);

         //Latest scan date
         var latestScanDate = await dashboardDataAccess.GetLatestScanDate(userId);
         DateTime scansByHourStartDate;
         int chosenTimeInHours = 168;

         if (dashboardParams.ScansByHourStartDate == null)
         {
            var now = DateTime.UtcNow;
            var timeSinceLastScan = now - latestScanDate;

            DateTime roundedNow = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);

            if (timeSinceLastScan.TotalHours > 48)
            {
               scansByHourStartDate = roundedNow.AddDays(-7);
            }
            else if (timeSinceLastScan.TotalHours > 24)
            {
               chosenTimeInHours = 48;
               scansByHourStartDate = roundedNow.AddDays(-2);
            }
            else if (timeSinceLastScan.TotalHours > 12)
            {
                chosenTimeInHours = 24;
                scansByHourStartDate = roundedNow.AddDays(-1);
            }
            else
            {
                chosenTimeInHours = 12;
                scansByHourStartDate = roundedNow.AddHours(-12);
            }
         }
         else
         {
            scansByHourStartDate = (DateTime)dashboardParams.ScansByHourStartDate;
         }

         //Scans by hour
         List<ScanByHourItem> scansByHourItems = await dashboardDataAccess.GetScansByHourItems(scansByHourStartDate, dashboardParams.ChosenStockCheckId, dashboardParams.EndDate, userId);
         scansByHourItems = FillMissingHours(scansByHourItems, scansByHourStartDate);

         DateTime heatmapStartDate;
         if (dashboardParams.HeatmapStartDate == null)
         {
            heatmapStartDate = scansByHourStartDate;
         }
         else
         {
            heatmapStartDate = (DateTime)dashboardParams.HeatmapStartDate;
         }

         //Heatmap items
         decimal[][] scansCoordinatesLastHour = await dashboardDataAccess.GetHeatmapItems(heatmapStartDate, dashboardParams.ChosenStockCheckId, dashboardParams.EndDate, userId);

         Dashboard dashboardResult = new Dashboard()
         {
            StockCheckSummaryItems = summaryItems,
            ScansByHourItems = scansByHourItems,
            HeatmapItems = scansCoordinatesLastHour,
            ChosenTimeInHours = chosenTimeInHours
         };

         return dashboardResult;
      }

      public async Task<IEnumerable<StockCheckQualityItem>> GetStockCheckQualityRowData(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate)
      {
         return await dashboardDataAccess.GetStockCheckQualityRowData(userId, isActive, fromDate, toDate);
      }

      public async Task<IEnumerable<StockCheckSpeedItem>> GetStockCheckSpeedRowData(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate)
      {
         return await dashboardDataAccess.GetStockCheckSpeedRowData(userId, isActive, fromDate, toDate);
      }

      public async Task<List<ResolutionTypeItem>> GetResolutionTypeRows(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate)
      {
         var rows = await dashboardDataAccess.GetDashboardBasicInfoItems(userId, isActive, fromDate, toDate);
         var missing = await dashboardDataAccess.GetDashboardMissingResolutions(userId, isActive, fromDate, toDate);
         var unknown = await dashboardDataAccess.GetDashboardUnknownResolutions(userId, isActive, fromDate, toDate);
         var missingAuto = await dashboardDataAccess.GetDashboardMissingAutoResolutions(userId, isActive, fromDate, toDate);
         var unknownAuto = await dashboardDataAccess.GetDashboardUnknownAutoResolutions(userId, isActive, fromDate, toDate);

            var missingLookup = missing.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new ResolutionReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());

         var unknownLookup = unknown.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new ResolutionReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());

        var missingAutoLookup = missingAuto.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new ResolutionReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());

        var unknownAutoLookup = unknownAuto.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new ResolutionReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());
            foreach (var row in rows)
         {
            if (missingLookup.TryGetValue(row.StockCheckId, out var missingRes))
               row.MissingResolutions = missingRes;

            if (unknownLookup.TryGetValue(row.StockCheckId, out var unknownRes))
               row.UnknownResolutions = unknownRes;

            if (missingAutoLookup.TryGetValue(row.StockCheckId, out var missingAutoRes))
               row.MissingAutoResolutions = missingAutoRes;

            if (unknownAutoLookup.TryGetValue(row.StockCheckId, out var unknownAutoRes))
                row.UnknownAutoResolutions = unknownAutoRes;
            }

         return rows.ToList();
      }


      public async Task<List<DataLoadedItem>> GetDataLoadedItems(int userId, bool? isActive, DateTime? fromDate, DateTime? toDate)
      {
         var rows = await dashboardDataAccess.GetDataLoadedBasicInfoItems(userId, isActive, fromDate, toDate);
         var missing = await dashboardDataAccess.GetDataLoadedMissingReports(userId, isActive, fromDate, toDate);
         var unknown = await dashboardDataAccess.GetDataLoadedUnknownReports(userId, isActive, fromDate, toDate);

         var missingLookup = missing.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new DataLoadedReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());

         var unknownLookup = unknown.GroupBy(x => x.StockCheckId)
                                    .ToDictionary(g => g.Key, g => g.Select(x => new DataLoadedReportVM { Description = x.Description, UsageCount = x.UsageCount }).ToList());

         foreach (var row in rows)
         {
            if (missingLookup.TryGetValue(row.StockCheckId, out var missingRes))
               row.MissingReports = missingRes;

            if (unknownLookup.TryGetValue(row.StockCheckId, out var unknownRes))
               row.UnknownReports = unknownRes;
         }

         return rows.ToList();
      }

      private List<ScanByHourItem> FillMissingHours(List<ScanByHourItem> existing, DateTime startHour)
      {
         // Ensure 'now' is aligned to the hour
         var now = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.UtcNow.Day, DateTime.UtcNow.Hour, 0, 0);
         var current = startHour;

         var filled = new List<ScanByHourItem>();

         while (current <= now)
         {
            var existingEntry = existing.FirstOrDefault(e => e.Hour == current);
            filled.Add(existingEntry ?? new ScanByHourItem { Hour = current, Count = 0 });
            current = current.AddHours(1);
         }

         return filled;
      }

   }
}
