import { Component, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from '../../services/selections.service';

@Component({
    selector: 'confirmModal',
    templateUrl: './confirmModal.component.html',
    styleUrls: ['./confirmModal.component.scss'],
    standalone: false
})
export class ConfirmModalComponent {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  confirmModalHeader: string;
  isDestructive:boolean;

  constructor(
    public selections: SelectionsService,
    public modalService: NgbModal
  ) { }

  showModal() {
    this.modalService.open(this.modalRef, { windowClass: "confirmModal", size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.selections.confirmModalEmitter.next(true);
    }, (reason) => {
      this.selections.confirmModalEmitter.next(false);
    });
  }
}
