import { Component, OnInit, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { ConstantsService } from '../../../services/constants.service'
import { SelectionsService } from '../../../services/selections.service'
import { GetDataService } from '../../../services/getData.service'
import { CphPipe } from '../../../cph.pipe'
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component'
import { Subscription, take } from 'rxjs';
import { ColumnApi, DomLayoutType, GridApi, GridOptions } from 'ag-grid-community/dist/lib/main';
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';
import { LogoService } from 'src/app/services/logo.service';
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { BarComponent } from 'src/app/_cellRenderers/bar.component';
import { ToastService } from 'src/app/services/newToast.service';
import { StockCheckExcel } from '../../../model/StockCheckExcel';
import { StockCheck } from 'src/app/model/StockCheck';
import { ExcelExportService } from 'src/app/services/excelExport';
import { StockChecksService } from '../stockChecks.service';

@Component({
    selector: 'stockChecksTable',
    templateUrl: './stockChecksTable.component.html',
    styleUrls: ['./stockChecksTable.component.scss'],
    standalone: false
})

export class StockChecksTableComponent implements OnInit {
  @Input() public rowData: Array<StockCheck>;
  @Input() public viewHeight: number;
  @Input() public tableType: string;

  @Output() clickedRow = new EventEmitter<StockCheck>();
  @Output() selectedStockChecks = new EventEmitter<number[]>();

  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  showGrid: boolean = false;
  gridApi: GridApi;
  gridColumnApi: ColumnApi;
  mainTableGridOptions: GridOptions;
  excelDownloadSubscription: Subscription;
  totalSelectedRows: number;
  refreshGridSubscription: Subscription;
  selectedStockCheckId: number;

  clickCount: number = 0;

  // Track if the process is running by accessing the service's importInProgress
  isProcessRunning: boolean = false;
  chosenStockCheckId: number;

  rowHeight: number = 25;
  headerHeight: number = 55;
  groupHeaderHeight: number = 30;
  floatingFilterHeight: number = 30;
  domLayout: DomLayoutType;
  sidenavToggledSubscription: Subscription;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public data: GetDataService,
    public cphPipe: CphPipe,
    public logo: LogoService,
    public toastService: ToastService,
    private excelExportService: ExcelExportService,
    private stockChecksService: StockChecksService
  ) {

  }

  ngOnDestroy() {
    //if (this.excelDownloadSubscription) this.excelDownloadSubscription.unsubscribe()
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
    
  }

  ngOnInit() {
    this.workoutDomLayout();
    this.chosenStockCheckId = this.selections.stockCheck?.id;

    // There seems to be issues with referencing this on constants directly?
    // if(!this.stockChecksService.currencySymbol)
    // {
    //   this.stockChecksService.currencySymbol = this.constants.currencySymbol;
    // }

    this.getGridOptions();

    this.refreshGridSubscription = this.stockChecksService.refreshGridEmitter.subscribe((res: StockCheck) => {
      const rowData: StockCheck[] = this.stockChecksService.stockCheckVMs;
      this.workoutDomLayout();

      if (res) {
        this.gridApi.getSelectedNodes().forEach((node) => {
          node.updateData(res);
        })
      } else {
        this.gridApi.setRowData(rowData.filter(x => !x.isRegional && !x.isTotal));
        this.gridApi.setPinnedBottomRowData(rowData.filter(x => x.isRegional || x.isTotal));
        this.gridApi.refreshCells();
      }

      this.gridApi.sizeColumnsToFit();
    })

    // Subscribe to changes in the importInProgress flag
    this.stockChecksService.importInProgressObservable.subscribe((inProgress: boolean) => {
      this.isProcessRunning = inProgress;

      if (this.isProcessRunning) {
        // Disable grid when the process starts
        this.gridApi.showLoadingOverlay();
      } else {
        // Re-enable the grid when the process ends
        if(this.gridApi)
        {
          this.gridApi.hideOverlay();
        }
        
      }
    });

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  getGridOptions() {
    this.mainTableGridOptions = {
      onSelectionChanged: (event) => this.onSelectionChanged(event),
      // onRowClicked: (event) => this.onSelectionChanged(event),
      rowSelection: 'multiple',
      context: { thisComponent: this },
      onRowDoubleClicked: (params) => {
        this.onCellDoubleClick(params);
      },
      rowHeight: this.rowHeight,
      groupHeaderHeight: this.groupHeaderHeight,
      floatingFiltersHeight: this.floatingFilterHeight,
      headerHeight: this.headerHeight,
      components: { agColumnHeader: CustomHeaderComponent },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        width: 100,
        floatingFilter: true
      },
      onRowDataChanged: (params) => { this.rowDataChanged() },
      columnTypes: {
        currency: { cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) }, cellClass: 'agAlignRight', filter: 'agNumberColumnFilter', },
        currencyBadInRed: { cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) }, cellClass: (params) => {
          return `${ params.value > 0 ? 'badFont agAlignRight' : 'agAlignRight' }`
        }, filter: 'agNumberColumnFilter', },
        currencyBadInRedRegular: { cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0, true) }, cellClass: (params) => { return `${ params.value > 0 || params.value < 0 ? 'badFontRegular agAlignRight' : 'agAlignRight' }` }, filter: 'agNumberColumnFilter', },
        number: { cellClass: 'agAlignRight', filter: 'agNumberColumnFilter', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'number', 0) }, width: 40 },
        numberBadInRed: {
          cellClass: (p) => p.value > 0 ? 'badFont agAlignRight' : 'agAlignRight', filter: 'agNumberColumnFilter',
          cellRenderer: (params) => this.cphPipe.transform(params.value, 'number', 0), width: 40
        },
        percent: { filter: 'agNumberColumnFilter', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 0) } },
        dateMed: { cellClass: 'agAlignCentre', cellRenderer: (params) => this.cphPipe.transform(params.value, 'dateMed', 0), filter: 'agDateColumnFilter', width: 50, filterParams: this.getFilterParams() },
        dateTimeNoYear: { cellClass: 'agAlignCentre', cellRenderer: (params) => params.value === '' || !params.value ? '' : this.cphPipe.transform(params.value, 'dateTimeNoYear', 0), filter: 'agDateColumnFilter', width: 50, filterParams: this.getFilterParams() },
        label: { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', width: 50, },
      },
      columnDefs: this.getColDefs(),
      rowData: this.stockChecksService.stockCheckVMs.filter(x => !x.isRegional && !x.isTotal),
      getRowClass: (params) => {
        if (params.data.isRegional) return 'regionalRow';
        if (params.data.isTotal) return 'totalRow';
      },
      rowClassRules: {
        'activeStockcheck': (params) => params.data.id === this.chosenStockCheckId,
        'hideCheckboxChecked': (params) => this.totalSelectedRows <= 1,
        'ag-row-selected': (params) => (params.data.isRegional || params.data.isTotal) && params.data.id === this.selectedStockCheckId
      },
      pinnedBottomRowData: this.stockChecksService.stockCheckVMs.filter(x => x.isRegional || x.isTotal),
      immutableData: true,  //immutableData and getRowNodeId to be used together.
      getRowNodeId: (data) => data.id,
      onColumnGroupOpened: (event) => {
        this.gridApi.sizeColumnsToFit();
        this.stockChecksService.columnState = this.gridColumnApi?.getColumnState();
      },
      onColumnMoved: (event) => {
        this.stockChecksService.columnState = this.gridColumnApi?.getColumnState();
      },
      onFilterChanged: (event) => {
        this.stockChecksService.filterState = this.gridApi.getFilterModel();
      },
      onSortChanged: (event) => {
        this.stockChecksService.columnState = this.gridColumnApi?.getColumnState();
      },
      
    }
  }

  getColDefs() {
    let colDefs =
      [
        { headerName: 'Id', field: 'id', type: 'label', width: 25, floatingFilterComponentParams: { suppressFilterButton: true } },
        { headerName: 'Site', field: 'site', type: 'label', width: 70 },
        { headerName: 'Division', field: 'siteRegion', type: 'label', width: 50 },
        { headerName: 'Stock Check Date', valueGetter: (params) => { return new Date(params.data.date).toISOString().split('T')[0]; }, type: 'dateMed' },
        { headerName: 'Last Updated', type: 'dateTimeNoYear', valueGetter: params => { return params.data.isRegional || params.data.isTotal ? '' : params.data.lastUpdated }, width: 50  },
        { headerName: 'Last Updated by', type: 'label', valueGetter: params => { return params.data.isRegional || params.data.isTotal ? '' : params.data.person }, width: 50  },
        { headerName: 'Status', type: 'label', valueGetter: params => { return this.constants.abbreviateStockCheckStatus(params.data.status); } },
        { headerName: 'Completed by', field: 'approvedByAccountant', type: 'label', },
        { headerName: 'Approved by', field: 'approvedBy', type: 'label', },
        { headerName: 'In Stock', field: 'stockItemsCount', type: 'number' },
        { headerName: `In Stock (${this.constants.currencySymbol}'000)`, field: 'inStockValue', type: 'currency', width: 40 },
        { headerName: `GL Value (${this.constants.currencySymbol}'000)`, field: 'glValue', type: 'currency', width: 40 },
        { headerName: `Variance (${this.constants.currencySymbol}'000)`, field: 'variance', type: 'currencyBadInRedRegular', width: 40 },
        { headerName: 'Scans', field: 'scans', type: 'number' },
        { headerName: 'In Stock and Scanned', field: 'scannedInStock', type: 'number', },
        {
          headerName: 'Resolved Differences', children: [
            { headerName: 'Missing', field: 'missings', type: 'number', },
            { headerName: 'Unknown', field: 'unknowns', type: 'number', },
          ]
        },
        {
          headerName: 'Unresolved Differences', children: [
            { headerName: 'Missing', field: 'missingOs', type: 'numberBadInRed', },
            { headerName: `Missing (${this.constants.currencySymbol}'000)`, field: 'unresolvedMissingValue', type: 'currencyBadInRed', width: 40 },
            { headerName: 'Unknown', field: 'unknownOs', type: 'numberBadInRed', },
          ]
        },
        {
          headerName: 'Percentage Complete', field: 'percentageComplete', cellRendererFramework: BarComponent, width: 90, cellRendererParams: { good: 100, bad: 50 },
          comparator: (valueA, valueB, nodeA, nodeB, isDescending) => {
            let actualValueA: number = valueA;
            let actualValueB: number = valueB;

            if (nodeA.data.scans === 0 && nodeA.data.stockItemsCount === 0 && nodeA.data.statusId > 2) {
              actualValueA = 1;
            }

            if (nodeB.data.scans === 0 && nodeB.data.stockItemsCount === 0 && nodeB.data.statusId > 2) {
              actualValueB = 1;
            }

            return actualValueA - actualValueB;
          }
        },
        {
          headerName: '',
          children: [
            { headerName: '', colId: 'selectRow', field: '', width: 20, headerCheckboxSelection: true, checkboxSelection: true }
          ]
        },
      ];

    if (this.constants.shouldUploadSignoffImage) {
      return colDefs.filter(x => x.headerName !== 'Completed by' && x.headerName !== 'Approved by')
    }
    else {
      return colDefs;
    }


    
  }

  rowDataChanged() {
    setTimeout(() => {
      this.gridApi.sizeColumnsToFit()
    }, 500)
  }

  onCellDoubleClick(params) {
    this.chosenStockCheckId = params.data.id;
    this.clickedRow.next(params.data);

    // if(params.data.isRegional)
    // {
    //   this.onSelectRegionalRow(params);
      
    // }

    
    this.gridApi.redrawRows();
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this };
    if (this.stockChecksService.columnState) {
      this.gridColumnApi.applyColumnState({ state: this.stockChecksService.columnState, applyOrder: true });
    }
    if (this.stockChecksService.filterState) {
      this.gridApi.setFilterModel(this.stockChecksService.filterState);
    }
    this.gridApi.sizeColumnsToFit();
    this.showGrid = true;
  }

  generateExcelSheet() {
    this.toastService.loadingToast('Generating Excel file...');

    let rowsToInclude: StockCheckExcel[] = [];

    this.gridApi.forEachNodeAfterFilter(n => {
      if (!!n.data) {
        rowsToInclude.push({
          Id: n.data.id.toString(), 
          Site: n.data.site,
          Region: n.data.siteRegion,
          StockcheckDate: n.data.date,
          FirstScan: n.data.firstScan ? new Date(n.data.firstScan) : null,
          LastScan: n.data.lastScan ? new Date(n.data.lastScan) : null,
          LastUpdated: n.data.lastUpdated,
          LastUpdatedBy: n.data.person,
          Status: n.data.status,
          MarkedCompleteBy: n.data.approvedByAccountant,
          ApprovedBy: n.data.approvedBy,
          InStock: n.data.stockItemsCount,
          InStockValue: n.data.inStockValue,
          GlValue: n.data.glValue,
          Variance: n.data.variance,
          Scans: n.data.scans,
          ScannedAndInStock: n.data.scannedInStock,
          TotalIssuesMissingVehicles: n.data.missings,
          TotalIssuesUnknownVehicles: n.data.unknowns,
          OutstandingIssuesMissingVehicles: n.data.missingOs,
          OutstandingIssuesMissingValue: n.data.unresolvedMissingValue,
          OutstandingIssuesUnknownVehicles: n.data.unknownOs,
          PercentageComplete: n.data.scans === 0 && n.data.stockItemsCount === 0 && n.data.statusId > 2 ? 1 : Math.floor(n.data.percentageComplete * 100) / 100,
          IsRegional: false,
          IsTotal: false
        });
      }
    })

    const totalPinnedBottomRows: number = this.gridApi.getPinnedBottomRowCount();
    if (totalPinnedBottomRows > 0) {
      for (let i = 0; i < totalPinnedBottomRows; i++) {
        const n = this.gridApi.getPinnedBottomRow(i);
        
        if (!!n.data) {
          rowsToInclude.push({
            Id: n.data.id.toString(),
            Site: n.data.site,
            Region: n.data.siteRegion,
            StockcheckDate: n.data.date,
            FirstScan: n.data.firstScan ? new Date(n.data.firstScan) : null,
            LastScan: n.data.lastScan ? new Date(n.data.lastScan) : null,
            LastUpdated: n.data.lastUpdated,
            LastUpdatedBy: n.data.person,
            Status: n.data.status,
            MarkedCompleteBy: n.data.approvedByAccountant,
            ApprovedBy: n.data.approvedBy,
            InStock: n.data.stockItemsCount,
            InStockValue: n.data.inStockValue,
            GlValue: n.data.glValue,
            Variance: n.data.variance,
            Scans: n.data.scans,
            ScannedAndInStock: n.data.scannedInStock,
            TotalIssuesMissingVehicles: n.data.missings,
            TotalIssuesUnknownVehicles: n.data.unknowns,
            OutstandingIssuesMissingVehicles: n.data.missingOs,
            OutstandingIssuesMissingValue: n.data.unresolvedMissingValue,
            OutstandingIssuesUnknownVehicles: n.data.unknownOs,
            PercentageComplete: n.data.scans === 0 && n.data.stockItemsCount === 0 && n.data.statusId > 2 ? 1 : Math.floor(n.data.percentageComplete * 100) / 100,
            IsRegional: n.data.isRegional,
            IsTotal: n.data.isTotal
          });
        }
      }
    }

    let sheet = {
      tableData: rowsToInclude,
      tableName: `Stock Checks - ${rowsToInclude.length}`,
      columnWidths: this.excelExportService.workoutColWidths(rowsToInclude)
    }

    this.downloadExcelFile(sheet);
  }

  downloadExcelFile(sheet: SheetToExtractOld) {
    const workbook = new excelJS.Workbook();
    const currency = this.constants.currencySymbol;
    const worksheet = workbook.addWorksheet(sheet.tableName);
  
    // Add Logo
    workbook.addImage({
      base64: this.logo.provideStockPulseLogo(),
      extension: 'png'
    });
  
    // Freeze Pane and Zoom
    worksheet.views = [{ state: 'frozen', xSplit: 1, ySplit: 5, zoomScale: 85 }];
  
    // Set column widths
    sheet.columnWidths = sheet.columnWidths.splice(0, sheet.columnWidths.length - 2);
    worksheet.columns = sheet.columnWidths.map(w => ({ width: w }));
  
    // Add title and subtitle rows
    let titleRow = worksheet.addRow([sheet.tableName]);
    titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };
  
    let subtitle = `Extracted ${this.cphPipe.transform(new Date(), 'time', 0)} ${this.cphPipe.transform(new Date(), 'date', 0)} by ${this.selections.usersName}`;
    let subTitleRow = worksheet.addRow([subtitle]);
    subTitleRow.font = { name: 'Calibri', family: 4, size: 12, bold: false, italic: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };
  
    worksheet.addRow([]);
    worksheet.addRow([]);
  
    worksheet.getRow(1).height = 28;
  
    // Format header row
    const columnHeadersRaw = Object.keys(sheet.tableData[0]);
    const columnHeadersFormatted = columnHeadersRaw.map(key => this.formatColumnHeader(key, currency));
    const columnHeadersFinal = columnHeadersFormatted.map(header => header.replace('Total Issues', '').replace('Outstanding Issues', '').trim())
                                                 .filter(x => x !== 'Is Total' && x !== 'Is Regional');
  
    const indices = this.getColumnIndices(columnHeadersFormatted, currency);
    worksheet.addRow(columnHeadersFinal);
    this.formatHeaderRows(worksheet, columnHeadersFormatted, indices.outstandingIssuesIndex);
  
    // Add and style data rows
    const maxColIndex = 21;
    sheet.tableData.forEach(x => this.addDataRow(worksheet, x, maxColIndex));
  
    const rowCount = worksheet.rowCount + 1;
    
    for (let i = 6; i < rowCount; i++) {
      this.formatDataRow(worksheet, i, indices, currency);
    }
  
    this.autoResizeColumns(worksheet);
  
    const workbookName = `StockPulse Extract ${new Date().getDate()}${new Date().toLocaleString('en-gb', { month: 'short' })}${new Date().getFullYear()}`;
  
    workbook.xlsx.writeBuffer().then(data => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, `${workbookName}.xlsx`);
    });
  
    this.toastService.destroyToast();
  }
  
  private formatColumnHeader(header: string, currency: string): string {
    const mapping = {
      InStockValue: `In Stock (${currency}'000)`,
      GlValue: `GL Value (${currency}'000)`,
      Variance: `Variance (${currency}'000)`,
      OutstandingIssuesMissingValue: `Missing (${currency}'000)`,
      Region: 'Division'
    };
  
    if (mapping[header]) return mapping[header];
  
    return header.replace('Vehicles', '').match(/[A-Z]+(?![a-z])|[A-Z]?[a-z]+|\d+/g).join(' ').trim();
  }
  
  private getColumnIndices(headers: string[], currency: string) {
    return {
      outstandingIssuesIndex: headers.findIndex(x => x === 'Outstanding Issues Missing'),
      percentageCompleteIndex: headers.findIndex(x => x.includes('Percent')),
      stockcheckDateIndex: headers.findIndex(x => x === 'Stockcheck Date'),
      lastUpdatedIndex: headers.findIndex(x => x === 'Last Updated'),
      inStockValueIndex: headers.findIndex(x => x === `In Stock (${currency}'000)`),
      glValueIndex: headers.findIndex(x => x === `GL Value (${currency}'000)`),
      varianceIndex: headers.findIndex(x => x === `Variance (${currency}'000)`),
      missingValueIndex: headers.findIndex(x => x === `Missing (${currency}'000)`),
      totalIssuesMissingValueIndex: headers.findIndex(x => x === 'Total Issues Missing'),
      totalIssuesUnknownValueIndex: headers.findIndex(x => x === 'Total Issues Unknown'),
      scansValueIndex: headers.findIndex(x => x === 'Scans'),
      scanAndInStockValueIndex: headers.findIndex(x => x === 'Scanned And In Stock'),
      unknownValueIndex: headers.findIndex(x => x === 'Outstanding Issues Unknown'),
      firstScanIndex: headers.findIndex(x => x === 'First Scan'),
      lastScanIndex: headers.findIndex(x => x === 'Last Scan')
    };
  }
  
  private formatHeaderRows(worksheet, headers: string[], outstandingIssuesIndex: number) {
    headers.forEach((_, i) => {
      const colLetter = String.fromCharCode(65 + i);
      ['4', '5'].forEach(row => {
        worksheet.getCell(`${colLetter}${row}`).font = { name: 'Calibri', size: 11, color: { argb: 'FFFFFFFF' } };
        worksheet.getCell(`${colLetter}${row}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } };
      });
    });
    worksheet.getCell(String.fromCharCode(65 + outstandingIssuesIndex) + '4').value = 'Unresolved Differences';
    worksheet.getCell(String.fromCharCode(65 + outstandingIssuesIndex - 2) + '4').value = 'Resolved Differences';
  }
  
  private addDataRow(worksheet, rowData, maxColIndex: number) {
    const values = Object.values(rowData).splice(0, Object.values(rowData).length - 2);
    const row = worksheet.addRow(values);
    if (rowData.IsRegional || rowData.IsTotal) {
      const fillColor = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: rowData.IsTotal ? 'C5CCD3' : 'F1F2F4' },
        bgColor: { argb: rowData.IsTotal ? 'C5CCD3' : 'F1F2F4' }
      };
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        if (colNumber <= maxColIndex) cell.fill = fillColor;
      });
    }
  }
  
  private formatDataRow(worksheet, rowIndex: number, indices, currency: string) {
    const valueCols = [6, 7, 8, 9, 10, 11, 12];
    valueCols.forEach(j => {
      const colLetter = String.fromCharCode(65 + j);
      worksheet.getCell(`${colLetter}${rowIndex}`).numFmt = '#,##0;[Red]-#,##0;[Black]-';
    });
  
    [indices.outstandingIssuesIndex, indices.outstandingIssuesIndex + 1].forEach(index => {
      const colLetter = String.fromCharCode(65 + index);
      worksheet.getCell(`${colLetter}${rowIndex}`).font = { name: 'Calibri', size: 11, bold: false, color: { argb: 'FF0000' } };
      worksheet.getCell(`${colLetter}${rowIndex}`).numFmt = '#,##0;[Red]-#,##0;[Black]-';
    });
  
    const percentCol = String.fromCharCode(65 + indices.percentageCompleteIndex);
    worksheet.getCell(`${percentCol}${rowIndex}`).numFmt = '0%';
    worksheet.getCell(`${percentCol}${rowIndex}`).alignment = { horizontal: 'right', vertical: 'middle' };
  
    ['stockcheckDateIndex', 'lastUpdatedIndex'].forEach(key => {
      const colLetter = String.fromCharCode(65 + indices[key]);
      worksheet.getCell(`${colLetter}${rowIndex}`).numFmt = 'dd/mm/yyyy'
      worksheet.getCell(`${colLetter}${rowIndex}`).alignment = { horizontal: 'left', vertical: 'middle' };
    });

    ['firstScanIndex', 'lastScanIndex'].forEach(key => {
      const colLetter = String.fromCharCode(65 + indices[key]);
      worksheet.getCell(`${colLetter}${rowIndex}`).alignment = { horizontal: 'left', vertical: 'middle' };
      worksheet.getCell(`${colLetter}${rowIndex}`).numFmt = 'dd/mm/yyyy hh:mm:ss';
    });
  
    const formattedCols = [
      ['inStockValueIndex', false],
      ['glValueIndex', false],
      ['varianceIndex', true],
      ['missingValueIndex', true],
      ['unknownValueIndex', true],
    ] as const;
    
    formattedCols.forEach(([key, isBold]) => {
      const colLetter = String.fromCharCode(65 + indices[key]);
      const cell = worksheet.getCell(`${colLetter}${rowIndex}`);
    
      const val = cell.value;
      if (key === 'missingValueIndex' || key === 'unknownValueIndex') {
        cell.font = { name: 'Calibri', size: 11, bold: false, color: { argb: val > 0 ? 'FF0000' : '000000' } };
        cell.numFmt = `"${currency}"#,##0;"-${currency}"#,##0;[Black]-`;
      } else if (key === 'varianceIndex') {
        cell.font = { name: 'Calibri', size: 11, bold: false, color: { argb: 'FF0000' } };
        cell.numFmt = `"+${currency}"#,##0;"-${currency}"#,##0;[Black]-`;
      } else {
        cell.numFmt = `"${currency}"#,##0;"-${currency}"#,##0;[Black]-`;
      }
    });
    
  
    ['totalIssuesMissingValueIndex', 'totalIssuesUnknownValueIndex', 'scansValueIndex', 'scanAndInStockValueIndex', 'unknownValueIndex'].forEach(key => {
      const colLetter = String.fromCharCode(65 + indices[key]);
      worksheet.getCell(`${colLetter}${rowIndex}`).numFmt = '#,##0;-#,##0;[Black]-';
    });
  }
  
  private autoResizeColumns(worksheet) {
    worksheet.columns.forEach((col, i) => {
      let width = 10;
      if (i === 0) {
        width = Math.max(...Object.values(col.values).slice(2).map(val => val.toString().length));
      } else if (col.values[6] instanceof Date) {
        width = col.values[5].length;
      } else {
        width = Math.max(...Object.values(col.values).map(val => val.toString().length));
      }
      col.width = width + 3;
    });
  }

  onSelectionChanged(event: any) {
    const allowedEvents: string[] = ['rowClicked', 'checkboxSelected', 'uiSelectAll'];
    
    if (allowedEvents.includes(event.source)) {
      
      this.clickCount += 1;

      setTimeout(() => {
        if (this.clickCount === 1) {
          this.clickCount = 0;

          const selectedRows = event.api.getSelectedRows();
          this.totalSelectedRows = selectedRows.length;
          let selectedStockcheckIds: number[] = [];

          selectedRows.forEach((selectedRow, index) => {
            selectedStockcheckIds.push(selectedRow.id);
          });

          this.selectedStockChecks.emit(selectedStockcheckIds);
        } else {
          this.clickCount = 0;
        }
      }, 250)
    }
  }

  onSelectRegionalRow(params: any) {

    var selectedStockcheckIds: number[] = [];

    selectedStockcheckIds.push(params.data.id);

    this.selectedStockChecks.emit(selectedStockcheckIds)
  }

  getFilterParams() {
    return {
      buttons: ['clear'],
      comparator: (filterLocalDateAtMidnight: Date, cellDate: any) => {
        if (cellDate instanceof Date) {
          cellDate.setHours(0, 0, 0, 0);
        } else {
          let dateParts: string[] = cellDate.split('T')[0].split('-');
          cellDate = new Date(
            Number(dateParts[0]),
            Number(dateParts[1]) - 1,
            Number(dateParts[2])
          )
        }
        
        if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) {
          return 0;
        }
        if (cellDate < filterLocalDateAtMidnight) {
          return -1;
        }
        if (cellDate > filterLocalDateAtMidnight) {
          return 1;
        }
        return 0;
      },
      inRangeFloatingFilterDateFormat: "Do MMM YYYY"
    }
  }

  workoutDomLayout() {
    const contentNewHeight: number = document.getElementsByClassName('content-new')[0].clientHeight;
    const rowCount: number = this.stockChecksService.stockCheckVMs.length;

    // rowCount * rowHeight = total rows in pixels
    // factor in headerHeight in px (Including group header and floating filter header)
    // check if overall rowHeight + headerHeight is greater than content-new height
    if (rowCount * this.rowHeight + (this.headerHeight + this.groupHeaderHeight + this.floatingFilterHeight) < contentNewHeight) {
      this.domLayout = 'autoHeight';
    } else {
      this.domLayout = 'normal';
    }
  }
}
