import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ConstantsService } from "../services/constants.service";
import { Thresholds } from "../model/Thresholds";
import { BarClasses } from "../model/BarClasses";

@Component({
    selector: 'bar-cell',
    template: `
        <div class="barHolder">
            <div class="barActual" [ngClass]="barClass" [ngStyle]="{ 'width.%': width }">
        </div>
        <div class="barLabel">{{ (value / 100) | cph:'percent':0 }}</div>
        <div *ngIf="params.data.isTotal || params.data.isRegional" class="regionalTotalLabel">
            {{ params.data.percentageCompleteExtra }}
        </div>
    `,
    styles: [`
        .barHolder {
            width: 90%;
            height: 80%;
            display: flex;
            overflow: hidden;
            background: var(--grey90);
            margin: 0 auto;
        }
        
        .barActual { position: relative; transition: ease all 0.3s; }
        .barLabel { position: absolute; width: 90%; display: flex; align-items: center; justify-content: flex-end ; top: 0; bottom: 0; padding-right: 10px; }
        .regionalTotalLabel { position: absolute; top: 0; left: 0; padding-left: calc(5% + 10px); }
        .ok { background-color: var(--secondary); }
        .good { background-color: var(--success); }
        .bad { background-color: var(--danger); }
    `],
    standalone: false
})
export class BarComponent implements ICellRendererAngularComp {
    thresholds: Thresholds;
    value: number;
    barClass: BarClasses;
    width: number;
    params: any;

    constructor(
        public constants: ConstantsService
    ) { }

    agInit(params: any): void {
        this.params = params;
        this.value = Math.floor(params.value * 100);

        const data = this.params.node.data;
        if (data.scans === 0 && data.stockItemsCount === 0 && data.statusId > 2) {
            this.value = 100;
        }

        this.thresholds = {
            good: params.column.userProvidedColDef.cellRendererParams.good,
            bad: params.column.userProvidedColDef.cellRendererParams.bad
        }

        this.barClass = {
            good: this.value >= this.thresholds.good,
            ok: this.value < this.thresholds.good && this.value >= this.thresholds.bad,
            bad: this.value < this.thresholds.bad
        }
        
        setTimeout(() => {
            this.width = this.value;
        }, 150)
    }

    refresh(): boolean {
        return false;
    }
}
