export function  getDurationText(from: Date, to: Date): string {
  const diffMs = Math.abs(to.getTime() - from.getTime());
  const diffMinutes = Math.round(diffMs / (1000 * 60)); // total minutes, rounded
  const totalHours = Math.round(diffMinutes / 60); // round to nearest hour
  const days = Math.floor(totalHours / 24);
  const hours = totalHours % 24;

  const parts = [];
  if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`);
  if (hours > 0 || days === 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);

  return parts.join(', ');
}