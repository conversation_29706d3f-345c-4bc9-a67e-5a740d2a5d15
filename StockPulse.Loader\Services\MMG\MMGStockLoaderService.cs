﻿using Aspose.Cells.Drawing;
using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Services.Jardine;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Repository.Database;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Loader.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services.MMG
{


   public class MMGStockLoaderService : GenericLoaderJobServiceParams
   {

      //constructor
      public MMGStockLoaderService()
      {

      }

      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "mmg";
         string filePattern = "*StockPulseCurrentStock*.csv";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.MMGStockItems,
            customerFolder = "mmg",
            filename = filePattern,
            importSPName = null,
            loadingTableName = "StockItems",
            jobName = "MMGStock",
            pulse = PulsesService.STK_MMGStock,
            fileType = FileType.csv,
            regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
            headerFailColumn = null,
            headerDefinitions = null,
            errorCount = 0,
            dealerGroupId = 11,
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
         };

         return parms;
      }


      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.StockItem> incomingLines = new List<Model.Input.StockItem>();
         var missingSitesDictionary = new Dictionary<string, int>();

         MMGSharedService mmgSharedService = new MMGSharedService(logMessage);

         int incomingProcessCount = 0;

         incomingLines = new List<Model.Input.StockItem>(10000);  //preset the list size (slightly quicker than growing it each time)

         int total = rowsAndHeaders.rowsAndCells.Count;

         logMessage.FailNotes = "";

         using (var db = new StockpulseContext())
         {
            // Main sites
            List<Site> sites = db.Sites
                .AsNoTracking()
                .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId && s.IsActive)
                .ToList();

            // Build valid SiteId set
            HashSet<int> validSiteIds = sites.Select(s => s.Id).ToHashSet();
            
            IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary
                                                                                 .Where(x => x.DealerGroupId == parms.dealerGroupId && validSiteIds.Contains(x.SiteId))
                                                                                 .AsNoTracking().AsEnumerable();

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

               try
               {
                  string siteName = rowCols[0].ToString();

                  if (siteName == "Toyota Bristol South") { continue; }

                  SiteDescriptionDictionary siteDictionary = siteDescriptionDictionary.Where(x => x.Description == siteName && x.IsPrimarySiteId).FirstOrDefault();

                  // Unable to find Site
                  if (siteDictionary == null)
                  {
                     if (mmgSharedService.IsSiteSkippable(siteName))
                     {
                        continue;
                     }
                     else
                     {
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }

                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }
                  }

                  // Found site in dictionary - get SiteId (will go to catch if not found)
                  int siteId = siteDictionary.SiteId;

                  Model.Input.StockItem incomingStock = new Model.Input.StockItem()
                  {
                     SiteId = siteId,
                     Branch = siteName,
                     Reg = rowCols[2]?.ToString().Replace(" ", "") ?? "",
                     Vin = rowCols[3].ToString().Trim(),
                     Description = rowCols[4].ToString(),
                     DIS = GetNullableInt(rowCols[5]),
                     GroupDIS = GetNullableInt(rowCols[6]),
                     Comment = rowCols[7].ToString(),
                     StockType = rowCols[8].ToString(),
                     Reference = rowCols[9].ToString(),
                     StockValue = GetDecimal(rowCols[10]),
                     FileImportId = parms.fileImportId,
                     SourceReportId = 1,
                     DealerGroupId = parms.dealerGroupId,
                  };


                  if (incomingStock.Reg == "" && incomingStock.Vin == "") { incomingStock.Reg = "ROW" + incomingProcessCount.ToString(); };

                  incomingLines.Add(incomingStock);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         missingSitesDictionary = missingSitesDictionary
            .OrderBy(kvp => kvp.Key) // Sort by siteName
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingLines.ToDataTable();
         result.Columns.Remove("Sites");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("DealerGroup");
         result.Columns.Remove("SourceReports");
         return result;
      }

      private decimal GetDecimal(string raw)
      {
         try
         {
            return decimal.Parse(raw);
         }
         catch
         {
            return 0;
         };
      }

      private int? GetNullableInt(string raw)
      {
         try
         {
            return int.Parse(raw);
         }
         catch
         {
            return null;
         };
      }

      private string GetVinLastEightChars(string raw)
      {
         try
         {
            string vinRaw = raw.ToString().Trim();
            int length = vinRaw.Length;
            return vinRaw.Substring(length - 8);
         }
         catch
         {
            return null;
         }

      }




   }
}
