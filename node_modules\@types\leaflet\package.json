{"name": "@types/leaflet", "version": "1.9.17", "description": "TypeScript definitions for leaflet", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "alejo90", "url": "https://github.com/alejo90"}, {"name": "<PERSON><PERSON>", "githubUsername": "atd-schubert", "url": "https://github.com/atd-schubert"}, {"name": "<PERSON>", "githubUsername": "m<PERSON>uer", "url": "https://github.com/mcauer"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ronikar"}, {"name": "<PERSON>", "githubUsername": "life777", "url": "https://github.com/life777"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/henry<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "captain-<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/captain-igloo"}, {"name": "<PERSON>", "githubUsername": "someonewithpc", "url": "https://github.com/someonewithpc"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet"}, "scripts": {}, "dependencies": {"@types/geojson": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "43c209f5704bdfdb25a0c1401b4e2b4651495b4ad76fa83b2c2bc677b2995a15", "typeScriptVersion": "5.0"}