<div class="content-new">
  <div
    *ngIf="dashboardService.dashboard && !dashboardService.tableView"
    class="dashboard-grid"
  >
    <!-- Current stock checks -->
    <div class="dashboard-tile grid-row-1-2 grid-col-1-4">
      <div class="tile-header">
        <h3>Current Stock Checks summary</h3>
        <button
          *ngIf="dashboardService.chosenStockCheckId"
          class="btn btn-primary"
          (click)="clearChosenStockCheck()"
        >
          Remove filter
        </button>
      </div>
      <div class="tile-body">
        <stockCheckSummary
          [data]="dashboardService.dashboard.stockCheckSummarySimple"
        ></stockCheckSummary>
      </div>
    </div>

    <div class="dashboard-tile grid-row-1-2 grid-col-4-6">
      <div class="tile-header"><h3>Status of Current Stock Checks</h3></div>
      <div class="tile-body">
        <stockCheckStatus></stockCheckStatus>
      </div>
    </div>

    <!-- Scans by hour -->
    <div class="dashboard-tile grid-row-2-3 grid-col-1-4">
      <div class="tile-header">
        <h3>
          Scans - {{ dashboardService.chosenScansByHourTimePeriod.friendly }}
          <span *ngIf="dashboardService.chosenStockCheckSite">
            - {{ dashboardService.chosenStockCheckSite }}</span
          >
          ({{
            constantsService.pluralise(
              totalScansByHour,
              "scan",
              "scans"
            )
          }})
        </h3>
        <div
          class="d-inline-block"
          #dropdown="ngbDropdown"
          container="body"
          ngbDropdown
        >
          <button
            id="dropdownTrigger"
            class="btn btn-primary"
            ngbDropdownToggle
          >
            {{ dashboardService.chosenScansByHourTimePeriod.friendly }}
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownTrigger">
            <button
              *ngFor="let period of dashboardService.scansByHourTimePeriods"
              ngbDropdownItem
              (click)="chooseScansByHourTimePeriod(period)"
            >
              {{ period.friendly }}
            </button>
          </div>
        </div>
      </div>
      <div class="tile-body">
        <scansByHour></scansByHour>
      </div>
    </div>

    <!-- Heatmap -->
    <div class="dashboard-tile grid-row-2-3 grid-col-4-6">
      <div class="tile-header">
        <h3>
          Scanning Heat Map -
          {{ dashboardService.chosenHeatmapTimePeriod.friendly }}
          <span *ngIf="dashboardService.chosenStockCheckSite">
            - {{ dashboardService.chosenStockCheckSite }}</span
          >
          ({{
            constantsService.pluralise(
              dashboardService.dashboard.heatmapItems?.length,
              "scan",
              "scans"
            )
          }})
        </h3>
        <div>
          <button
            *ngIf="dashboardService.userHasInteractedWithMap"
            class="btn btn-primary me-2"
            (click)="resetBoundary()"
          >
            Reset boundary
          </button>
          <div
            class="d-inline-block"
            #dropdown="ngbDropdown"
            container="body"
            ngbDropdown
          >
            <button
              id="dropdownTrigger"
              class="btn btn-primary"
              ngbDropdownToggle
            >
              {{ dashboardService.chosenHeatmapTimePeriod.friendly }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownTrigger">
              <button
                *ngFor="let period of dashboardService.heatmapTimePeriods"
                ngbDropdownItem
                (click)="chooseHeatmapTimePeriod(period)"
              >
                {{ period.friendly }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="tile-body">
        <heatmap #heatmap></heatmap>
      </div>
    </div>
  </div>
</div>
