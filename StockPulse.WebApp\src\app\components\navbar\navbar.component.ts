import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ToastService } from 'src/app/services/newToast.service';
import { MsalService } from '@azure/msal-angular';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ReviewImportMapsModal } from 'src/app/pages/loadItems/reviewImportMapsModal/reviewImportMapsModal.component';
import { SiteNameLookupsModalComponent } from '../siteNameLookupsModal/siteNameLookupsModal.component';
import { PreferenceKey } from 'src/app/model/UserPreference';
import { UserPreferenceService } from 'src/app/services/userPreference.service';
import { ReconcileService } from 'src/app/pages/reconcile/reconcile.service';
import { Chart } from 'chart.js';


@Component({
    selector: 'app-navbar',
    templateUrl: './navbar.component.html',
    styleUrls: ['./navbar.component.scss'],
    standalone: false
})
export class NavbarComponent implements OnInit {
  //@ViewChild('accountPopover', { static: true }) accountPopover: NgbPopover;
  finishAnimationSubscription: Subscription;
  animateRefreshIcon: boolean = false;

  constructor(
    public icon: IconService,
    public reconService: ReconcileService,
    public selections: SelectionsService,
    public constants: ConstantsService,
    public router: Router,
    public api: ApiAccessService,
    private authService:MsalService,
    private toast:ToastService,
    public modalService: NgbModal,
    private userPreferenceService: UserPreferenceService
  ) { }

  ngOnInit(): void {
    this.finishAnimationSubscription = this.constants.refreshPage.subscribe((res) => {
      this.animateRefreshIcon = res;
    })
  }

  ngOnDestroy(): void {
    if (this.finishAnimationSubscription) this.finishAnimationSubscription.unsubscribe();
  }

  pageNeedsRefresh() {
    let pagesWithRefresh: string[] = ['/stockChecks', '/reconcile', '/dashboard'];
    if (this.router.url === '/reconcile' && this.selections.stockCheck.statusId > 3) return false;
    return pagesWithRefresh.includes(this.router.url);
  }

  emitRefresh() {
    this.reconService.drawBarsFromZero = false;
    this.animateRefreshIcon = false;
    this.constants.refreshPage.emit(true);
  }

  logout() {
   // this.accountPopover.close();
   this.selections.userId = null;
    if (this.constants.userLoggedInThroughAzureAD){
      localStorage.clear();
      this.authService.logout();
    }
    else{
      localStorage.clear();
      this.router.navigateByUrl('/login')
    }
  }

  goToHome() {
    this.router.navigateByUrl('/home');
  }

  // selectDealerGroup(selectedDealerGroupVM: DealerGroupVM ){
  //   const toastRef = this.toast.successToast('Will update dealergroup then restart app...')
  //   this.api.post('User','UpdateDealerGroup', selectedDealerGroupVM).subscribe((userDetails: any) => {
  //     toastRef.updateMessage('Succesfully updated')
  //     setTimeout(()=>{
  //       window.location.reload();
  //     },500)
  //   }, e => {
  //     //
  //   }, () =>{
  //     //
  //   });
  // }
  
  changeTheme() {
    this.constants.lightTheme = !this.constants.lightTheme;
    let body: HTMLElement = document.getElementById('body');
    
    if (this.constants.lightTheme) {
      body.classList.remove('regular-theme');
      body.classList.add('light-theme');
    } else {
      body.classList.remove('light-theme');
      body.classList.add('regular-theme');
    }
  }

  getLogo() {
    return this.constants.lightTheme ? './assets/imgs/stockpulseLogoBlack.svg' : './assets/imgs/stockpulseLogoWhite.svg';
  }

  openScanLocationsModal() {
    this.constants.scanLocationsModal.showModal();
  }

  openReviewImportMapsModal() {
    const modalRef = this.modalService.open(ReviewImportMapsModal);
    modalRef.result.then(res => {
      // Do something with result
    })
  }

  openSiteNameLookupsModal() {
    const modalRef = this.modalService.open(SiteNameLookupsModalComponent);
    modalRef.result.then(res => {
      // Do something with result
    })
  }

  get navbarClass() {
    if (this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed)) {
      return 'fixSideMenu';
    };
  }

  switchDealerGroup() {
    const charts = Chart.instances;
    for (const key in charts) {
      if (charts[key]) {
        charts[key].destroy();
      }
    }

    let mySubscription = this.selections.dealerGroupSelectionModalEmitter.subscribe(res =>{
      mySubscription.unsubscribe();
      if(res){
        window.location.reload();
      }
    })

    this.constants.dealerGroupSelectionModal.showModal()  }
}
