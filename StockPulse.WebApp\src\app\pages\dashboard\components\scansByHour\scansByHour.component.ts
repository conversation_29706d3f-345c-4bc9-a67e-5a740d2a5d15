import {
  Component,
  OnInit,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>roy,
} from "@angular/core";
import { Subscription } from "rxjs";
import { Chart } from "chart.js/auto";
import { CphPipe } from "src/app/cph.pipe";
import { ScanByHour } from "src/app/model/ScanByHour";
import { ConstantsService } from "src/app/services/constants.service";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { DashboardService } from "../../dashboard.service";

@Component({
  selector: "scansByHour",
  templateUrl: "./scansByHour.component.html",
  styleUrls: ["./scansByHour.component.scss"],
})
export class ScansByHourComponent implements OnInit, OnDestroy {
  public chart: any;
  public noScans: boolean;

  private subscription: Subscription;

  constructor(
    private cphPipe: CphPipe,
    public constantsService: ConstantsService,
    private dashboardService: DashboardService
  ) {}

  get data(): ScanByHour[] {
    return this.dashboardService.dashboard.scansByHourItems;
  }

  ngOnInit(): void {
    if (this.constantsService.sum(this.data.map((x) => x.count)) === 0) {
      this.noScans = true;
    } else {
      this.createChart();
    }

    // Subscribe to the newDataEmitter
    this.subscription = this.dashboardService.newDataEmitter.subscribe(() => {
      this.reactToChanges();
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    this.dashboardService.userHasInteractedWithMap = false;
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  reactToChanges() {
    if (this.constantsService.sum(this.data.map((x) => x.count)) === 0) {
      if (this.chart) {
        this.chart.destroy();
        this.chart = null;
      }
      this.noScans = true;
      return;
    } else {
      this.noScans = false;
    }

    if (this.chart) {
      this.chart.data = this.formatDataForChart();
      this.chart.update();
    } else {
      setTimeout(() => {
        this.createChart();
      }, 50);
    }
  }

  createChart() {
    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = new Chart("scansByHour", {
      type: "bar",
      data: this.formatDataForChart(),
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: false,
        scales: {
          y: {
            beginAtZero: true,
          },
          x: {
            ticks: {
              autoSkip: false,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
          datalabels: {
            anchor: "end",
            align: "end",
            formatter(value, ctx) {
              return ctx.dataset.data[ctx.dataIndex] !== 0
                ? ctx.dataset.data[ctx.dataIndex].toLocaleString()
                : null;
            },
            display: (ctx) => ctx.dataset.data[ctx.dataIndex] !== 0,
          },
        },
        layout: {
          padding: {
            top: 30,
          },
        },
      },
      plugins: [ChartDataLabels],
    });
  }

  groupByDay(data: ScanByHour[]) {
    const sorted: ScanByHour[] = data.sort(
      (a, b) => new Date(a.hour).getTime() - new Date(b.hour).getTime()
    );

    const grouped: ScanByHour[] = [];

    for (let i = 0; i < sorted.length; i += 24) {
      const dayChunk: ScanByHour[] = sorted.slice(i, i + 24);
      const totalCount: number = dayChunk.reduce(
        (sum, item) => sum + item.count,
        0
      );

      grouped.push({
        hour: dayChunk[0].hour,
        count: totalCount,
      });
    }

    return grouped;
  }

  formatDataForChart() {
    if (this.data.length === 169) {
      const data = this.groupByDay(this.data);
      return {
        labels: data.map((x) => this.cphPipe.transform(x.hour, "shortDate", 0)),
        datasets: [
          {
            data: data.map((x) => x.count),
            backgroundColor: "#FFBF00",
          },
        ],
      };
    } else {
      return {
        labels: this.data.map((x) =>
          this.cphPipe.transform(x.hour, "hour12", 0)
        ),
        datasets: [
          {
            data: this.data.map((x) => x.count),
            backgroundColor: "#FFBF00",
          },
        ],
      };
    }
  }
}
