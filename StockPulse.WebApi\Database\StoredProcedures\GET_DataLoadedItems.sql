CREATE OR ALTER PROCEDURE [dbo].[GET_DataLoadedItems]
(
    @UserId INT = NULL
)  
AS  
BEGIN
    SET NOCOUNT ON;    

    DECLARE @DealerGroupId INT;
    SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

    SELECT
        sc.Id AS StockCheckId,
        s.Description AS Site,
        d.Description AS Division,
        sc.Date AS StockCheckDate,
        st.Description AS Status,
        cu.Name AS CompletedBy,

        (
            SELECT COUNT(*) 
            FROM StockItems si 
            WHERE si.StockCheckId = sc.Id
        ) AS InStock,

        (
            SELECT COUNT(*) 
            FROM Scans sca 
            WHERE sca.StockCheckId = sc.Id
        ) AS Scans,

        -- MISSING REPORTS
        (
            SELECT
                rt.Description,
                COUNT(ri.Id) AS UsageCount
            FROM ReconcilingItemTypes rt
            LEFT JOIN ReconcilingItems ri 
                ON ri.ReconcilingItemTypeId = rt.Id AND ri.StockCheckId = sc.Id
            WHERE rt.IsActive = 1 
                AND rt.ExplainsMissingVehicle = 1
                AND rt.DealerGroupId = @DealerGroupId
            GROUP BY rt.Description
            FOR JSON AUTO
        ) AS MissingReports,

        -- UNKNOWN REPORTS
        (
            SELECT
                rt.Description,
                COUNT(ri.Id) AS UsageCount
            FROM ReconcilingItemTypes rt
            LEFT JOIN ReconcilingItems ri 
                ON ri.ReconcilingItemTypeId = rt.Id AND ri.StockCheckId = sc.Id
            WHERE rt.IsActive = 1 
                AND rt.ExplainsMissingVehicle = 0
                AND rt.DealerGroupId = @DealerGroupId
            GROUP BY rt.Description
            FOR JSON AUTO
        ) AS UnknownReports

    FROM StockChecks sc
    INNER JOIN Sites s ON s.Id = sc.SiteId
    INNER JOIN Statuses st ON st.Id = sc.StatusId
    INNER JOIN Divisions d ON d.Id = s.DivisionId
    LEFT JOIN Users cu ON cu.Id = sc.ApprovedByAccountantId

    WHERE
        sc.IsActive = 1
        AND d.DealerGroupId = @DealerGroupId
        AND sc.IsRegional = 0
        AND sc.IsTotal = 0

    GROUP BY
        s.Id, s.Description, st.Description, cu.Name, sc.Date, sc.Id, d.Description
    ORDER BY
        sc.Date DESC, s.Description
END
GO
