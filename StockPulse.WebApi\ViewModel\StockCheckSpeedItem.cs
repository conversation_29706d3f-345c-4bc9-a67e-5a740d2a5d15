﻿using System;

namespace StockPulse.WebApi.ViewModel
{
   public class StockCheckSpeedItem
   {
      private int? CalcHours(DateTime? oldDate, DateTime? newDate)
      {
         if (oldDate.HasValue && newDate.HasValue)
         {
            var timespan = (DateTime)newDate - (DateTime)oldDate;
            return (int)timespan.TotalHours;
         }
         else
         {
            return null;
         }
      }


      public int StockCheckId { get; set; }
      public string Site { get; set; }
      public string Division { get; set; }
      public DateTime StockCheckDate { get; set; }
      public string Status { get; set; }
      public string CompletedBy { get; set; }
      public string ApprovedBy { get; set; }
      public DateTime? StockLoaded { get; set; }
      public int Scans { get; set; }
      public DateTime? FirstScan { get; set; }
      public DateTime? LastScan { get; set; }
      //public DateTime? LastUnknownResolved { get; set; }
      public DateTime? LastMissingResolved { get; set; }
      public int? ScanDurationHours => CalcHours(FirstScan, LastScan);
      public DateTime? ReconciliationCompleted { get; set; }
      public int? TimeToCompleteHours => CalcHours(FirstScan, ReconciliationCompleted);
      public int? TimeToApproveHours => CalcHours(ReconciliationCompleted, ReconciliationApproved);
      public int? TotalDurationHours => CalcHours(FirstScan, ReconciliationApproved);
      public DateTime? ReconciliationApproved { get; set; }

      public int? TimeToResolveUnknownsHours { get; set; }

      public int? TimeToResolveMissingsHours {get; set;}

   }
}
