import { Component, HostListener, Input } from "@angular/core";
import {
  ColDef,
  ColGroupDef,
  Column,
  ColumnApi,
  GridApi,
  GridO<PERSON>s,
  GridReadyEvent,
} from "ag-grid-community";
import { Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { ConstantsService } from "src/app/services/constants.service";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { ReportNameAndCount } from "src/app/model/ReportNameAndCount";
import { DataLoadedItem } from "src/app/model/DataLoadedItem";
import { DashboardService } from "../../dashboard.service";
import { LogoService } from "src/app/services/logo.service";
import { NavigationStart, Router } from "@angular/router";
import { ToastService } from "src/app/services/newToast.service";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { ExcelExportService } from "src/app/services/excelExport";

@Component({
  selector: "app-data-loaded",
  templateUrl: "./data-loaded.component.html",
  styleUrls: ["./data-loaded.component.scss"],
})
export class DataLoadedComponent {
  @HostListener("window:resize", [])
  private onresize(event) {
    if (this.gridApi) {
      this.adjustColumnSizing();
    }
  }

  public gridOptions: GridOptions;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;
  private sidenavToggledSubscription: Subscription;
  private routerSubscription: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    public dashboardService: DashboardService,

    private logoService: LogoService,
    private router: Router,
    private toastService: ToastService,
    private excelExportService: ExcelExportService
  ) {}

  ngOnDestroy(): void {
    this.sidenavToggledSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  ngOnInit(): void {
    this.initialiseGrid();

    this.sidenavToggledSubscription =
      this.constantsService.sidenavToggledEmitter.subscribe(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });

    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dashboardService.dataLoadedColumnState =
          this.gridColumnApi.getColumnState();
        this.dashboardService.dataLoadedfilterModel =
          this.gridApi.getFilterModel();
      }
    });
  }

  initialiseGrid(): void {
    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      headerHeight: 50,
      floatingFiltersHeight: 25,
      rowHeight: 25,
      rowData: this.dashboardService.dataLoadedRowData,
      context: { thisComponent: this },
      defaultColDef: {
        sortable: true,
        resizable: true,
        floatingFilter: true,
        autoHeight: true,
        autoHeaderHeight: true,
        wrapHeaderText: true,
      },
      columnTypes: {
        label: { filter: "agTextColumnFilter" },
        number: {
          filter: "agTextColumnFilter",
          cellClass: "agAlignRight",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "number", 0)
              : "-",
        },
        date: {
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "dateMed", 0)
              : null,
        },
      },
      columnDefs: this.buildColDefs(),
      onGridReady: (event) => this.onGridReady(event),
      onCellDoubleClicked: (event) => {
        let field: string = event.colDef.field;

        if (
          field.includes("Missing_") ||
          field.includes("Unknown_") ||
          field === "inStock"
        ) {
          this.dashboardService.loadStockCheckAndGoToImport(
            event.data.stockCheckId,
            event.colDef.field
          );
        }
      },
      onColumnGroupOpened: (event) => {
        this.gridApi.sizeColumnsToFit();
        this.dashboardService.dataLoadedColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onColumnMoved: (event) => {
        this.dashboardService.dataLoadedColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onFilterChanged: (event) => {
        this.dashboardService.dataLoadedfilterModel =
          this.gridApi.getFilterModel();
      },
      onSortChanged: (event) => {
        this.dashboardService.dataLoadedColumnState =
          this.gridColumnApi?.getColumnState();
      },
    };
  }

  private buildColDefs(): (ColDef<any> | ColGroupDef<any>)[] {
    return [
      {
        headerName: "Id",
        field: "stockCheckId",
        type: "label",
        width: 60,
        pinned: "left",
      },
      {
        headerName: "Site",
        field: "site",
        type: "label",
        width: 150,
        pinned: "left",
      },
      {
        headerName: "Division",
        field: "division",
        type: "label",
        width: 80,
        pinned: "left",
      },
      {
        headerName: "Stock Check Date",
        field: "stockCheckDate",
        type: "date",
        width: 100,
        pinned: "left",
      },
      {
        headerName: "Status",
        valueGetter: (params) => {
          return this.constantsService.abbreviateStockCheckStatus(
            params.data.status
          );
        },
        type: "label",
        width: 120,
        pinned: "left",
      },
      {
        headerName: "Completed By",
        field: "completedBy",
        type: "label",
        width: 100,
        pinned: "left",
      },
      {
        headerName: "In Stock",
        field: "inStock",
        type: "number",
        width: 75,
        cellClass: "hover-bold agAlignRight",
      },
      {
        headerName: "Missing Reports",
        children: this.getMissingColDefs(),
      },
      {
        headerName: "Unknown Reports",
        children: this.getUnknownColDefs(),
      },
      {
        headerName: "Scans",
        field: "scans",
        type: "number",
        width: 75,
        cellClass: "hover-bold agAlignRight",
      },
    ];
  }

  onGridReady(event: GridReadyEvent): void {
    this.gridApi = event.api;
    this.gridColumnApi = event.columnApi;

    if (this.dashboardService.dataLoadedColumnState) {
      this.gridColumnApi.applyColumnState({
        state: this.dashboardService.dataLoadedColumnState,
        applyOrder: true,
      });
    }
    if (this.dashboardService.dataLoadedfilterModel) {
      this.gridApi.setFilterModel(this.dashboardService.dataLoadedfilterModel);
    }

    this.adjustColumnSizing();
  }

  getMissingColDefs(): ColDef[] {
    if (!this.dashboardService.dataLoadedRowData || this.dashboardService.dataLoadedRowData.length === 0) return [];

    // Find all fields that start with "Missing_"
    const missingFields = Object.keys(this.dashboardService.dataLoadedRowData[0])
      .filter((key) => key.startsWith("Missing_"))
      .map((field) => ({
        headerName: field.replace("Missing_", ""),
        field: field,
        type: "number",
        width: 100,
        cellClass: "hover-bold agAlignRight",
      }));

    return missingFields;
  }

  getUnknownColDefs(): ColDef[] {
    if (!this.dashboardService.dataLoadedRowData || this.dashboardService.dataLoadedRowData.length === 0) return [];

    // Find all fields that start with "Unknown_"
    const unknownFields = Object.keys(this.dashboardService.dataLoadedRowData[0])
      .filter((key) => key.startsWith("Unknown_"))
      .map((field) => ({
        headerName: field.replace("Unknown_", ""),
        field: field,
        type: "number",
        width: 100,
        cellClass: "hover-bold agAlignRight",
      }));

    return unknownFields;
  }

  download(): void {
    this.toastService.loadingToast("Generating Excel file...");

    const filteredRows = [];
    const columns: Column[] = this.gridColumnApi.getAllDisplayedColumns();

    this.gridApi.forEachNodeAfterFilter((n) => {
      const columnsForExcel = {};

      columns.forEach((column) => {
        const columnId: string = column.getColId();
        columnsForExcel[columnId] = n.data[columnId];
      });

      columnsForExcel["stockCheckDate"] = new Date(n.data.stockCheckDate);

      filteredRows.push(columnsForExcel);
    });

    const colTypes: string[] = [];

    this.gridApi.getColumnDefs().forEach((colDef) => {
      if ((colDef as ColGroupDef).children) {
        (colDef as ColGroupDef).children.forEach((child) => {
          colTypes.push((child as ColDef).type as string);
        });
      } else {
        colTypes.push((colDef as ColDef).type as string);
      }
    });

    let sheet: SheetToExtractOld = {
      tableData: filteredRows,
      tableName: `Data Loaded`,
      columnWidths: this.excelExportService.workoutColWidths(filteredRows),
      colTypes: colTypes,
    };

    this.dashboardService.downloadExcelFile(sheet, 6);
  }

  get excelIconBackgroundImage() {
    return {
      "background-image": `url(${this.logoService.provideExcelLogo()})`,
    };
  }

  adjustColumnSizing(): void {
    if (!this.gridApi || !this.gridColumnApi) return;

    // Total width of all columns
    const allColumns = this.gridColumnApi.getAllDisplayedColumns();
    const totalColumnWidth = allColumns.reduce((sum, col) => {
      return (
        sum +
          this.gridColumnApi
            .getColumnState()
            .find((c) => c.colId === col.getColId())?.width || 0
      );
    }, 0);

    // Width of the grid container
    const gridElement = document.querySelector<HTMLElement>(
      "#dataLoadedGrid"
    );
    const gridWidth = gridElement?.offsetWidth || 0;

    if (totalColumnWidth < gridWidth) {
      this.gridApi.sizeColumnsToFit();
    }
  }
}
