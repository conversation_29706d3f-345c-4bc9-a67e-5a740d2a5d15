CREATE OR ALTER PROCEDURE [dbo].[GET_DataLoadedMissingReports]
(
    @UserId INT = NULL,
    @IsActive BIT,
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)  
AS  
BEGIN
    SET NOCOUNT ON;    

    DECLARE @DealerGroupId INT;
    SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

    SELECT
        sc.Id AS StockCheckId,
        rt.Description,
        COUNT(ri.Id) AS UsageCount
    FROM StockChecks sc
    INNER JOIN Sites s ON s.Id = sc.SiteId
    INNER JOIN Divisions d ON d.Id = s.DivisionId
    CROSS JOIN ReconcilingItemTypes rt
    LEFT JOIN ReconcilingItems ri ON ri.ReconcilingItemTypeId = rt.Id AND ri.StockCheckId = sc.Id
    WHERE
        (@IsActive IS NULL OR SC.IsActive = @IsActive)
        AND d.DealerGroupId = @DealerGroupId
        AND sc.IsRegional = 0
        AND sc.IsTotal = 0
        AND rt.IsActive = 1
        AND rt.ExplainsMissingVehicle = 1
        AND rt.DealerGroupId = @DealerGroupId
        AND (@FromDate IS NULL OR sc.Date >= @FromDate)
        AND (@ToDate IS NULL OR sc.Date <= @ToDate)
    GROUP BY
        sc.Id, rt.Description
    ORDER BY
        sc.Id, rt.Description
END
GO
