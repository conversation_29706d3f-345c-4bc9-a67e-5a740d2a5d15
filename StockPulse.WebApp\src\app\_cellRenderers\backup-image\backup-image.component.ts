import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ImageToUpdate } from 'src/app/model/ImageToUpdate';

@Component({
    selector: 'app-backup-image',
    templateUrl: './backup-image.component.html',
    styleUrls: ['./backup-image.component.scss'],
    standalone: false
})
export class BackupImageComponent implements ICellRendererAngularComp {
  resolutionImage: ImageToUpdate;
  modalImage: string;
  showDownloadFilePrompt: boolean;

  agInit(params: any): void {
    this.resolutionImage = params.data.resolutionImages ? params.data.resolutionImages[0] : null;
  }

  refresh(): boolean {
    return false;
  }

  updateUrl(resolutionImage: ImageToUpdate) {
    let imageElement: HTMLImageElement = document.getElementById(`backup-image-${resolutionImage.id}`) as HTMLImageElement;
    imageElement.src = this.imageForFileExtensions(resolutionImage.fileName);
    this.showDownloadFilePrompt = true;
  }

  imageForFileExtensions(nameOrSrc: string) {
    if (nameOrSrc.includes('.pdf')) {
      return './assets/imgs/backup-pdf.png';
    } else if (nameOrSrc.includes('.docx')) {
      return './assets/imgs/backup-word.png';
    } else if (nameOrSrc.includes('.xl') || nameOrSrc.includes('.csv')) {
      return './assets/imgs/backup-excel.png';
    } else {
      return './assets/imgs/backup-file.png';
    }
  }

  maybeDownloadFile(image: any) {
    let link = document.createElement("a");
    link.href = image.url;
    link.download = image.fileName;
    link.click();
  }
}
