import { Injectable } from '@angular/core';
import { Router, ActivatedRoute, ActivatedRouteSnapshot, RouterStateSnapshot, CanActivate } from '@angular/router';
import { SelectionsService } from './services/selections.service';
import { ConstantsService } from './services/constants.service';


@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate  {


  constructor(
    private router: Router,
    public selections: SelectionsService,
    private routeIs: ActivatedRoute,
    private constantsService: ConstantsService

  ) { }



  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    //no page, ok
    if (route.url.length === 0) { return true; }

    //always allowed to go to these
    if (['', 'home', 'vehicleSearch', 'stockChecks', 'labelPrinter', 'userMaintenance', 'dashboard'].includes(route.url[0].path)) { return true; }


    if (['tableMaintenance'].includes(route.url[0].path)) {
      return this.selections.userRole && this.constantsService.userRolePermissions.showTableMaintenance && (this.selections.userUsername.endsWith('@cphi.co.uk') || this.selections.userUsername.endsWith('@cphinsight.com'));// true// !this.selections.userRole
    }

    //otherwise, depends if you have loaded a stockcheck
    return !!this.selections.stockCheck








  }

}
