# Node.js with Angular
# Build a Node.js project that uses Angular.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
 tags:
    include:
    - webapp*
    - all*


pool:
  Default

jobs:
- job: BuildAndPublishAngularStockpulseClient
  displayName: 'Build And Publish Angular StockPulse Client'

  steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false  # Don't fetch unnecessary tags
    clean: true  # Ensures a fresh checkout
    persistCredentials: true

  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Install Node.js'

  - script: |
      npm install -g @angular/cli
      npm install -g brotli
    displayName: 'Install Angular CLI'

  - task: Npm@1
    displayName: 'Install npm packages'
    inputs:
      command: 'install'
      workingDir: '$(Build.SourcesDirectory)/StockPulse.WebApp'

  - task: Npm@1
    displayName: 'Build'
    inputs:
      command: 'custom'
      workingDir: '$(Build.SourcesDirectory)/StockPulse.WebApp'
      customCommand: 'run prod'

  - task: ArchiveFiles@2
    displayName: 'Zip'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)/StockPulse.WebApp/dist'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/stockpulseClient_$(Build.BuildId).zip'
      replaceExistingArchive: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/stockpulseClient_$(Build.BuildId).zip'
      ArtifactName: 'StockPulseClient'
      publishLocation: 'Container'

  - task: DeleteFiles@1
    displayName: 'Delete output folder'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)/StockPulse.WebApp/dist'
      Contents: '*' 
      RemoveSourceFolder: true