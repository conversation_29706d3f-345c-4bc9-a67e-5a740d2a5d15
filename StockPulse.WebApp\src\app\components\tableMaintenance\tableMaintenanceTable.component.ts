import { Component, HostListener, OnInit } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridOptions } from 'ag-grid-community';
import { CphPipe } from '../../cph.pipe';
import { TableMaintenanceService } from "./tableMaintenance.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { GetDataService } from "src/app/services/getData.service";
import { GridHelperService } from "src/app/services/gridHelper.service";
import { IconService } from "src/app/services/icon.service";
import { SaveDataService } from "src/app/services/saveData.service";





@Component({
    selector: 'tableMaintenanceTable',
    template: `

    <div id="tableContainer">


        <ag-grid-angular 
        id="varianceTable" 
        class="ag-theme-balham colourRows hoverRows mt-1" 
        [gridOptions]="mainTableGridOptions"
        
        
        >
        </ag-grid-angular>

      

    
    </div>
    




    `,
    styles: [`
.contentInner{top:0rem;}
#tableButtons{position:absolute;z-index:10;display: flex;
    align-items: flex-start;}
  #tableButtons .btn{height: 2.4em;    padding: 0px 1em;}

  .menuOption .menuLabel{padding: 0.2em 0.5em;color:white!important;}
  .menuOption{width:25em;}

  /* to vertically align the row headings on the left */
  :host ::ng-deep .ag-pinned-left-cols-container .ag-cell{padding-top:3px}
  :host ::ng-deep .ag-pinned-left-cols-container .ag-cell .ag-icon{margin-top:-4px}

  #tableOptionsButton{width: 11em;}

  .btn-primary.small{font-size:1.2em;}
    #tableContainer{position:relative;height:100%;width:100%;margin:0em;padding:1em;padding-top:0em; }
    #columnsToInclude{position:absolute;font-size:0.7em;top:6px;left:2px;z-index:10;}
    
    #counter{position:absolute;top:-2em;height:2em;right:0em;    background: var(--brightColourLighter);      padding: 0.2em 1em;      border-radius: 0.3em 0.3em 0 0;}
    h4{font-weight:700;}
    ag-grid-angular#varianceTable{height: 100%;width:100%}
    .numberChip{position:fixed;top:60px;right:20px;}
    #nodeControl{font-size:0.7em;}
    #nodeControl .btn{padding: 6px 12px;}

    #chooseActual,#chooseTarget{position:absolute;z-index:2;top:-3px;}
    .targetAndActualButtons{padding: 0.5em;}
    .actualAndTargetButtonInner{
      width: 80px;
    padding: 0em 1em 0em 0.2em;
    text-overflow: ellipsis;
    overflow: hidden;
  }
    
    :host ::ng-deep  .quantityAndPriceContainer{width:140px;}
    :host ::ng-deep .dummyBomItem{color:darkBlue;}

    :host ::ng-deep .ag-body-viewport .ag-row{cursor:pointer;}
    :host ::ng-deep .ag-body-viewport .ag-row-hover{background:var(--brightColourLightest)!important} 

    #okSummaryLevels{    width: 4em;    margin: 0em;}

    @media (min-width: 1366px) and (max-width: 1919px) {
      .actualAndTargetButtonInner{width:52px}
    } 
    
    
  `
    ],
    standalone: false
})


export class TableMaintenanceTableComponent implements OnInit {

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    //this.sizeGrid();
  }

  mainTableGridOptions: GridOptions;
  isDrilledToParts = false;

  constructor(
    public data: GetDataService,
    public modalService: NgbModal,
    
    public constants: ConstantsService,
    public cphPipe: CphPipe,

    public excel: ExcelExportService,
    public icon: IconService,
    public gridHelpers: GridHelperService,
    public save: SaveDataService,
    public service: TableMaintenanceService,


  ) { }


  ngOnInit(): void {

    
    this.initParams()

  }

  ngOnDestroy() {
    //if (this.service.gridApi) {
      // let model: any = this.service.gridApi.getModel()
      // let levelsAndKeys: { level: number, key: string, parent: string }[] = []
      // model.selectionController.rowModel.rowsToDisplay.forEach(item => {
      //   levelsAndKeys.push({ level: item.level, key: item.key, parent: item.parent.key })
      // })
      // this.service.openChildrenKeyNames = levelsAndKeys
    //}
    // this.service.gridApi = null;
    // this.service.gridColumnApi = null;
    // this.service.gridReference = null;
  }





  initParams() {
    this.setGridDefinitions()
  }


 


  setGridDefinitions() {

    const baseHeight = window.innerWidth > 1550 ? 4 : 0
    this.mainTableGridOptions = {
      animateRows: false,
      // getRowHeight: (params) => {
      //   const n = params.node.level
      //   if (params.data && params.data.IsProjectExBomItem && !params.node.rowPinned) return window.devicePixelRatio === 1.5 ? 18 : 20
      //   else if (n > 3) return window.devicePixelRatio === 1.5 ? 18 : 20 + baseHeight
      //   else if (n > 1) return window.devicePixelRatio === 1.5 ? 18 : 20 + baseHeight
      //   else if (n > 0) return window.devicePixelRatio === 1.5 ? 18 : 20 + baseHeight
      //   else return window.devicePixelRatio === 1.5 ? 18 : 20 + baseHeight
      // },

      defaultColDef: {
        sortable: true,
        resizable: true,
        filter: true,
        floatingFilter: true,
      },
      //frameworkComponents: { agColumnHeader: CustomHeaderComponent },
      onGridReady:(params)=>this.onGridReady(params),
      singleClickEdit: true,
      // autoGroupColumnDef: {
      //   headerName: '', pinned: 'left', width: 400, field: 'NameAndTitle', cellRendererParams: { suppressCount: true }, cellClass: (params) => this.gridHelpers.autoColumnCellClass(params.node),
      //   cellStyle: (params) => this.gridHelpers.autoColumnCellStyler(params), valueGetter: (params) => this.autoColumnGroupLabel(params),
      // },
      //pinnedTopRowData: [this.calculateTotalRow()],
      // statusBar: {
      //   statusPanels: [
      //     { statusPanel: 'agAggregationComponent', align: 'right' }
      //   ]
      // },
      
      //onCellClicked:(params)=>this.onCellClicked(params),
      enableRangeSelection: true,
      pivotColumnGroupTotals: "after",
      pivotMode: false,
      //onRowGroupOpened: (params) => this.sizeGrid(),
      suppressAggFuncInHeader: true,//hides the extra header row saying (Sum..)
      //getContextMenuItems: (params) => this.getContextMenuItems(params),
      //onRowClicked: (params) => this.onRowClicked(params),
      //onRowDoubleClicked: (params) => this.onRowDoubleClicked(params),
      rowBuffer: 0,
      //getRowClass: (params) => this.rowClassProvider(params),
      rowData: this.service.tableData,
      //processCellForClipboard: (params) => this.gridHelpers.processCellForClipboard(params, 'invest'),
      columnTypes: {
        "number": { cellClass: 'agAlignRight', cellRenderer: (params) => { if (!params.value) { return '-' } else return this.cphPipe.transform(params.value, 'currencyD', 0) }, },
        "alignRight": { cellClass: 'agAlignRight', },
        //"numberWithPlusMinus": { cellClass: 'agAlignRight', cellStyle: (params) => this.cellStyler(params), cellRenderer: (params) => { if (!params.value) { return '-' } else return this.cphPipe.transform(params.value, 'currencyD', 0, true) }, },
        "rorPopoverCell": { cellClass: 'agAlignRight', },
        "percent": { cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 0) } },
        "label": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', }
      },
      columnDefs:
        this.getColumnDefs()
    }
  }

    getColumnDefs(){
      return this.service.tableCols;
    }


  onGridReady(params) {
    this.service.gridApi = params.api;
    this.service.gridColumnApi = params.columnApi;
    this.service.gridReference = this;
  }

}



