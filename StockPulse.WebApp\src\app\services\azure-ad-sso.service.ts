import { Injectable } from "@angular/core";
import { ConstantsService } from "./constants.service";
import { MsalService } from "@azure/msal-angular";
import { AuthenticationResult, InteractionRequiredAuthError } from "@azure/msal-browser";

@Injectable({
  providedIn: "root",
})
export class AzureAdSSOService {
  constructor(
    private constants: ConstantsService,
    private msalService: MsalService
  ) {}

  async getAccessToken(doFroceRefresh: boolean): Promise<string | null> {
    if (!this.constants.userLoggedInThroughAzureAD) {
      // Local login token
      return this.constants.accessToken;
    }
    console.log("getting activeaccount");
    console.log("msalService.instance", this.msalService.instance);
    await this.msalService.instance.initialize();
    //await this.msalService.handleRedirectObservable();
    console.log("msalService", this.msalService);

    const activeAccount = this.msalService.instance.getActiveAccount();
    console.log("account", this.msalService.instance.getActiveAccount());
    if (!activeAccount) {

      //get the stockpulse app key entry from local storage
      const stockPulseAppKey = localStorage.getItem("msal.e1f38153-846a-4d51-8cb0-3ebd16b1590f.active-account-filters");
      if (doFroceRefresh && stockPulseAppKey != null) {
        await this.msalService.loginRedirect();
      }

      return null;
    }

    try {
      console.log("getting token");
      const response: AuthenticationResult =
        await this.msalService.instance.acquireTokenSilent({
          account: activeAccount,
          scopes: ["openid", "profile", "email"], // Replace with your actual API scopes
          forceRefresh: doFroceRefresh,
        });
      console.log("got token");
      console.log(response.idToken);
      //console.log(response.accessToken);
      //return "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GybdF3mCL7BmZhvqFYS8Fa0HSsW4ox3oHc8FDq4yeeWGrYsj_unkWPNd6Q3XyApd5QgOYT0GJlCqG8BUbVYk-hP1CB0-Lhlh8NO06Swoa0VRF_uHzzDYDn1uNR8H91pvx4rQR74Kx4KJU2Ayl9hb515n0U_8WfROkzXqdCPWaap0hhF1v2QWTDHBqvC_JqVg0a-6YEptwEDBZ0_Tlfk6dz0mfazi6Xw7n1CWnlTbMhRAzXRTJp9QpxqkfB08KYEIFbapB6qpuWrtyEs4BzG7wcwRTK8uXCAcLVzNysVn0fItmUJEWxfLWeg9KUDqePTEpT8x_5AI5viei9VKhcXbMw";

      return response.idToken;
    } catch (error) {
      console.error("Failed to get token", error);
      if (error instanceof InteractionRequiredAuthError) {
        // Silently failed, fall back to redirect
        console.error("Redirect to login");
        //this.msalService.loginRedirect();
      }
      return null;
    }
  }
}
