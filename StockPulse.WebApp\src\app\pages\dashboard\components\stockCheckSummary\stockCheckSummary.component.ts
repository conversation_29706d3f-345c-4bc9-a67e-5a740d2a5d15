import { Component, OnInit, Input, OnDestroy } from "@angular/core";
import { Subscription } from "rxjs";
import { GridReadyEvent, RowDoubleClickedEvent } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { StockCheckSummarySimple } from "src/app/model/StockCheckSummarySimple";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { BarComponent } from "src/app/_cellRenderers/bar.component";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { DashboardService } from "../../dashboard.service";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
  selector: "stockCheckSummary",
  templateUrl: "./stockCheckSummary.component.html",
  styleUrls: ["./stockCheckSummary.component.scss"],
  standalone: false,
})
export class StockCheckSummaryComponent implements OnInit {
  public gridOptions: GridOptionsCph;

  constructor(
    private cphPipe: CphPipe,
    public dashboardService: DashboardService,
    public constantsService: ConstantsService
  ) {}

  ngOnInit(): void {
    this.initialiseTable();
  }

  get data() {
    return this.dashboardService.dashboard.stockCheckSummaryItems;
  }
  initialiseTable() {
    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      headerHeight: 50,
      rowHeight: 25,
      columnTypes: {
        label: {
          cellClass: "agAlignLeft",
          filter: "agTextColumnFilter",
        },
        number: {
          cellClass: "agAlignRight",
          filter: "agNumberColumnFilter",
          cellRenderer: (params) => {
            return this.cphPipe.transform(params.value, "number", 0);
          },
        },
        date: {
          cellClass: "agAlignCentre",
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "dateMed", 0)
              : null,
        },
      },
      context: { thisComponent: this },
      defaultColDef: {
        sortable: true,
      },
      columnDefs: [
        { headerName: "Site", field: "site", type: "label", width: 70 },
        {
          headerName: "Stock Check Date",
          field: "stockCheckDate",
          type: "date",
          width: 40,
        },
        {
          headerName: "Status",
          valueGetter: (params) => {
            return this.constantsService.abbreviateStockCheckStatus(
              params.data.status
            );
          },
          type: "label",
          width: 75,
        },
        {
          headerName: "In Stock",
          field: "stockItems",
          type: "number",
          width: 35,
        },
        { headerName: "Scanned", field: "scans", type: "number", width: 35 },
        {
          headerName: "Missing Unresolved",
          field: "missingUnresolved",
          type: "number",
          width: 50,
        },
        {
          headerName: "Unknown Unresolved",
          field: "unknownUnresolved",
          type: "number",
          width: 50,
        },
        {
          headerName: "Percentage Complete",
          field: "percentageComplete",
          cellRendererFramework: BarComponent,
          width: 75,
          cellRendererParams: { good: 100, bad: 50 },
        },
      ],
      rowClassRules: {
        activeStockcheck: (params) =>
          params.data.id === this.dashboardService.chosenStockCheckId,
      },
      onGridReady: (event: GridReadyEvent) => {
        this.onGridReady(event);
      },
      onRowDoubleClicked: (event: RowDoubleClickedEvent) => {
        this.dashboardService.chosenStockCheckId = event.data.id;
        this.dashboardService.chosenStockCheckSite = event.data.site;
        this.dashboardService.getData();
      },
    };
  }

  onGridReady(event: GridReadyEvent) {
    event.api.sizeColumnsToFit();
  }
}
