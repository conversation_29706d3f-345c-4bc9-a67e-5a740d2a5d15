import { Injectable } from "@angular/core";
import { catchError, map, Observable, Subject, throwError } from "rxjs";
import { Dashboard } from "src/app/model/Dashboard";
import { TimePeriod } from "src/app/model/TimePeriod";
import { DashboardParams } from "src/app/model/DashboardParams";
import {
  DataLoadedItem,
  DataLoadedItemFlat,
} from "src/app/model/DataLoadedItem";
import { StockCheckSpeedItem } from "src/app/model/StockCheckSpeedItem";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { GetDataService } from "src/app/services/getData.service";
import { LogoService } from "src/app/services/logo.service";
import { ToastService } from "src/app/services/newToast.service";
import * as excelJS from "exceljs";
import * as fs from "file-saver";
import { CphPipe } from "src/app/cph.pipe";
import { SelectionsService } from "src/app/services/selections.service";
import { StockCheck } from "src/app/model/StockCheck";
import { StockCheckQualityRowData } from "src/app/model/StockCheckQualityRowData";
import { ConstantsService } from "src/app/services/constants.service";
import { Router } from "@angular/router";
import {
  ResolutionTypeItem,
  ResolutionTypesRowFlat,
} from "src/app/model/ResolutionTypeItem";
import { LoadItemsService } from "src/app/pages/loadItems/loadItems.service";
import { ColumnState } from "ag-grid-community";
import { ReconcileService } from "../reconcile/reconcile.service";
import { StockChecksService } from "../stockChecks/stockChecks.service";

export interface DashboardTableDataParams {
  showActive: boolean;
  fromDate: string;
  toDate: string;
}

@Injectable({
  providedIn: "root",
})
export class DashboardService {
  public tableView: boolean = false;
  public dashboard: Dashboard;
  public stockCheckQualityRowData: StockCheckQualityRowData[];
  public stockCheckSpeedRowData: StockCheckSpeedItem[];
  public resolutionTypesRowData: ResolutionTypesRowFlat[];
  public dataLoadedRowData: DataLoadedItemFlat[];
  public heatmapTimePeriods: TimePeriod[] = [
    { friendly: "1 hour", timeInHours: 1 },
    { friendly: "12 hours", timeInHours: 12 },
    { friendly: "1 day", timeInHours: 24 },
    { friendly: "2 days", timeInHours: 48 },
    { friendly: "1 week", timeInHours: 168 },
  ];
  public chosenHeatmapTimePeriod: TimePeriod;
  public scansByHourTimePeriods: TimePeriod[] = [
    { friendly: "12 hours", timeInHours: 12 },
    { friendly: "1 day", timeInHours: 24 },
    { friendly: "2 days", timeInHours: 48 },
    { friendly: "1 week", timeInHours: 168 },
  ];
  public chosenScansByHourTimePeriod: TimePeriod;
  public chosenStockCheckId: number;
  public chosenStockCheckSite: string;
  public userHasInteractedWithMap: boolean = false;

  // Observable that emits when new data is loaded
  public newDataEmitter = new Subject<void>();

  public stockCheckQualityColumnState: ColumnState[];
  public stockCheckQualityfilterModel: {
    [key: string]: any;
  };
  public stockCheckSpeedColumnState: ColumnState[];
  public stockCheckSpeedfilterModel: {
    [key: string]: any;
  };
  public resolutionTypesColumnState: ColumnState[];
  public resolutionTypesfilterModel: {
    [key: string]: any;
  };
  public dataLoadedColumnState: ColumnState[];
  public dataLoadedfilterModel: {
    [key: string]: any;
  };

  constructor(
    public apiAccess: ApiAccessService,
    public toastService: ToastService,
    public getDataService: GetDataService,
    public logoService: LogoService,
    public cphPipe: CphPipe,
    public selectionsService: SelectionsService,
    public constantsService: ConstantsService,
    public router: Router,
    private loadItemsService: LoadItemsService,
    private reconcileService: ReconcileService,
    private stockChecksService: StockChecksService
  ) {}

  getData(manualRefresh?: boolean) {
    let scansByHourStart: Date;
    let heatmapStart: Date;
    const now = new Date();
    const endDate: Date = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        now.getHours()
      );

    if (this.chosenScansByHourTimePeriod) {
      const baseDate = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        now.getHours()
      );

      // Calculate start dates based on the chosen time periods
      scansByHourStart = new Date(
        baseDate.getTime() -
          this.chosenScansByHourTimePeriod.timeInHours * 60 * 60 * 1000
      );
      heatmapStart = new Date(
        baseDate.getTime() -
          this.chosenHeatmapTimePeriod.timeInHours * 60 * 60 * 1000
      );
    }

    const dashboardParams: DashboardParams = {
      ScansByHourStartDate: scansByHourStart,
      HeatmapStartDate: heatmapStart,
      EndDate: endDate,
      ChosenStockCheckId: 0,
    };

    if (this.chosenStockCheckId) {
      dashboardParams.ChosenStockCheckId = this.chosenStockCheckId;
    }

    this.getDataService
      .getDashboard(dashboardParams)
      .subscribe((res: Dashboard) => {
        if (!this.chosenHeatmapTimePeriod) {
          this.chosenHeatmapTimePeriod = this.heatmapTimePeriods.find(x => x.timeInHours === res.chosenTimeInHours);
        }
        if (!this.chosenScansByHourTimePeriod) {
          this.chosenScansByHourTimePeriod = this.scansByHourTimePeriods.find(x => x.timeInHours === res.chosenTimeInHours);
        }
        if (this.dashboard) {
          this.dashboard.heatmapItems = res.heatmapItems;
          this.dashboard.scansByHourItems = res.scansByHourItems;
          this.dashboard.stockCheckSummaryItems = res.stockCheckSummaryItems;
        } else {
          this.dashboard = res;
        }
        this.toastService.destroyToast();

        // Emit event to notify components that new data is available
        this.newDataEmitter.next();

        if (manualRefresh) {
          this.constantsService.refreshPage.emit(false);
        }
      });
  }

  getStockCheckQualityRowData() {
    const params: DashboardTableDataParams = {
      showActive: this.stockChecksService.showActive,
      fromDate: this.stockChecksService.fromDate,
      toDate: this.stockChecksService.toDate,
    };

    this.getDataService
      .getStockCheckQualityRowData(params)
      .subscribe((data: StockCheckQualityRowData[]) => {
        this.stockCheckQualityRowData = data;
        this.toastService.destroyToast();
      });
  }

  getStockCheckSpeedRowData() {
    const params: DashboardTableDataParams = {
      showActive: this.stockChecksService.showActive,
      fromDate: this.stockChecksService.fromDate,
      toDate: this.stockChecksService.toDate,
    };

    this.getDataService
      .getStockCheckSpeedRowData(params)
      .subscribe((data: StockCheckSpeedItem[]) => {
        this.stockCheckSpeedRowData = data.map(
          (x) => new StockCheckSpeedItem(x)
        );
        this.toastService.destroyToast();
      });
  }

  async getResolutionTypesRowData() {
    const params: DashboardTableDataParams = {
      showActive: this.stockChecksService.showActive,
      fromDate: this.stockChecksService.fromDate,
      toDate: this.stockChecksService.toDate,
    };

    const data: ResolutionTypeItem[] =
      await this.getDataService.getResolutionTypesRowData(params);
    const resolutionTypesData: ResolutionTypesRowFlat[] =
      this.flattenItemsForAgGrid(data);
    return resolutionTypesData;
  }

  getDataLoadedRowData() {
    const params: DashboardTableDataParams = {
      showActive: this.stockChecksService.showActive,
      fromDate: this.stockChecksService.fromDate,
      toDate: this.stockChecksService.toDate,
    };

    this.getDataService
      .getDataLoadedItems(params)
      .subscribe((data: DataLoadedItem[]) => {
        this.dataLoadedRowData = this.modifyDataLoadedRowData(data);
        this.toastService.destroyToast();
      });
  }

  flattenItemsForAgGrid(items: ResolutionTypeItem[]): ResolutionTypesRowFlat[] {
    const allMissingDescriptions = new Set<string>();
    const allUnknownDescriptions = new Set<string>();
    const allMissingAutoDescriptions = new Set<string>();
    const allUnknownAutoDescriptions = new Set<string>();

    // First pass: collect all possible column names
    for (const item of items) {
      item.missingResolutions.forEach((r) =>
        allMissingDescriptions.add(r.description)
      );
      item.unknownResolutions.forEach((r) =>
        allUnknownDescriptions.add(r.description)
      );
      item.missingAutoResolutions.forEach((r) =>
        allMissingAutoDescriptions.add(r.description)
      );
      item.unknownAutoResolutions.forEach((r) =>
        allUnknownAutoDescriptions.add(r.description)
      );
    }

    // Second pass: flatten each row
    return items.map((item) => {
      const flatRow: any = {
        stockCheckId: item.stockCheckId,
        site: item.site,
        division: item.division,
        stockCheckDate: item.stockCheckDate,
        status: item.status,
        completedBy: item.completedBy,
        inStock: item.inStock,
        inStockAndScanned: item.inStockAndScanned,
        scans: item.scans,
      };

      flatRow["MissingAuto_Duplicate Stock Record"] =
        item.duplicateStockItems ?? 0;

      for (const desc of allMissingDescriptions) {
        const match = item.missingResolutions.find(
          (r) => r.description === desc
        );
        flatRow[`Missing_${desc}`] = match?.usageCount ?? 0;
      }

      for (const desc of allUnknownDescriptions) {
        const match = item.unknownResolutions.find(
          (r) => r.description === desc
        );
        flatRow[`Unknown_${desc}`] = match?.usageCount ?? 0;
      }

      for (const desc of allMissingAutoDescriptions) {
        const match = item.missingAutoResolutions.find(
          (r) => r.description === desc
        );
        flatRow[`MissingAuto_${desc}`] = match?.usageCount ?? 0;
      }

      for (const desc of allUnknownAutoDescriptions) {
        const match = item.unknownAutoResolutions.find(
          (r) => r.description === desc
        );
        flatRow[`UnknownAuto_${desc}`] = match?.usageCount ?? 0;
      }

      flatRow["UnknownAuto_Duplicate Scan"] = item.duplicateScans ?? 0;

      return flatRow;
    });
  }

  modifyDataLoadedRowData(data: DataLoadedItem[]): DataLoadedItemFlat[] {
    const allMissingDescriptions = new Set<string>();
    const allUnknownDescriptions = new Set<string>();

    // First pass: collect all possible column names
    for (const item of data) {
      item.missingReports.forEach((r) =>
        allMissingDescriptions.add(r.description)
      );
      item.unknownReports.forEach((r) =>
        allUnknownDescriptions.add(r.description)
      );
    }

    // Second pass: flatten each row
    return data.map((row) => {
      const flatRow: DataLoadedItemFlat = {
        stockCheckId: row.stockCheckId,
        site: row.site,
        division: row.division,
        stockCheckDate: row.stockCheckDate,
        status: row.status,
        completedBy: row.completedBy,
        inStock: row.inStock,
        scans: row.scans,
      };

      for (const desc of allMissingDescriptions) {
        const match = row.missingReports.find((r) => r.description === desc);
        flatRow[`Missing_${desc}`] = match?.usageCount ?? 0;
      }

      for (const desc of allUnknownDescriptions) {
        const match = row.unknownReports.find((r) => r.description === desc);
        flatRow[`Unknown_${desc}`] = match?.usageCount ?? 0;
      }

      return flatRow;
    });
  }

  downloadExcelFile(sheet: SheetToExtractOld, freezeAt?: number) {
    let workbook = new excelJS.Workbook();

    try {
      let worksheet = workbook.addWorksheet(sheet.tableName);

      worksheet.views = [
        { state: "frozen", xSplit: freezeAt ?? 7, ySplit: 5, zoomScale: 85 },
      ];

      let columns = [];

      sheet.columnWidths.forEach((w) => {
        columns.push({ width: w });
      });

      worksheet.columns = columns;

      //rows
      let titleRow = worksheet.addRow([sheet.tableName]);
      titleRow.font = {
        name: "Calibri",
        family: 4,
        size: 16,
        bold: true,
        color: "white",
        fill: { bgColor: { rgb: "220,230,241" } },
      };

      let subtitle: string = `Extracted ${this.cphPipe.transform(
        new Date(),
        "time",
        0
      )} ${this.cphPipe.transform(new Date(), "date", 0)} by ${
        this.selectionsService.usersName
      }`;

      let subTitleRow = worksheet.addRow([subtitle]);
      subTitleRow.font = {
        name: "Calibri",
        family: 4,
        size: 12,
        bold: false,
        italic: true,
        color: "white",
        fill: { bgColor: { rgb: "220,230,241" } },
      };

      worksheet.addRow([]);
      worksheet.addRow([]);

      worksheet.getRow(1).height = 28;

      let columnHeaders: string[] = Object.keys(sheet.tableData[0]);
      columnHeaders = columnHeaders.map((str) =>
        str
          .replace(/Auto_/, " -")
          .replace(/_/g, " -")
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (c) => c.toUpperCase())
          .replace(/ (Pct|Percent)/, " %")
          .replace(/Stock Check Id/, "Id")
      );

      worksheet.addRow(columnHeaders);
      let colCount: number = columnHeaders.length;

      for (let i = 0; i < colCount; i++) {
        let colLetter: string;

        if (i < 26) {
          colLetter = String.fromCharCode(65 + i);
        } else if (i < 52) {
          colLetter = `${String.fromCharCode(65)}${String.fromCharCode(
            65 + i - 26
          )}`;
        } else {
          colLetter = `${String.fromCharCode(66)}${String.fromCharCode(
            65 + i - 26
          )}`;
        }

        worksheet.getCell(colLetter + "4").font = {
          name: "Calibri",
          family: 4,
          size: 11,
          color: { argb: "FFFFFFFF" },
        };
        worksheet.getCell(colLetter + "4").fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF252424" },
        };
        worksheet.getCell(colLetter + "5").font = {
          name: "Calibri",
          family: 4,
          size: 11,
          color: { argb: "FFFFFFFF" },
        };
        worksheet.getCell(colLetter + "5").fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF252424" },
        };
      }

      sheet.tableData.forEach((x) => {
        worksheet.addRow(Object.values(x));
      });

      let rowCount: number = worksheet.rowCount + 1;

      // This goes down rows
      for (let i = 6; i < rowCount; i++) {
        // If 0, show dash
        for (let j = 0; j < colCount; j++) {
          let colLetter: string;

          if (j < 26) {
            colLetter = String.fromCharCode(65 + j);
          } else if (j < 52) {
            colLetter = `${String.fromCharCode(65)}${String.fromCharCode(
              65 + j - 26
            )}`;
          } else {
            colLetter = `${String.fromCharCode(66)}${String.fromCharCode(
              65 + j - 26
            )}`;
          }

          if (sheet.colTypes[j] === "percent") {
            worksheet.getCell(colLetter + i.toString()).numFmt = "0.0%;-0.0%;-";
          }

          if (sheet.colTypes[j] === "date") {
            worksheet.getCell(colLetter + i.toString()).numFmt = "d mmm yy";
          }

          if (sheet.colTypes[j] === "dayMonthTime") {
            worksheet.getCell(colLetter + i.toString()).numFmt = "d mmm hh:mm";
          }

          if (sheet.colTypes[j] === "number") {
            worksheet.getCell(colLetter + i.toString()).numFmt =
              "#,##0;-#,##0;-";
          }

          if (sheet.colTypes[j] === "time") {
            worksheet.getCell(colLetter + i.toString()).numFmt = '[h]"h" m"m"';
          }
        }
      }

      worksheet.columns.forEach((col, i) => {
        let width: number;

        if (i === 0) {
          width = Math.max(
            ...Object.values(col.values)
              .slice(2)
              .map((val) => val.toString().length)
          );
        } else if (col.values[6] instanceof Date) {
          width = col.values[5].length;
        } else {
          width = Math.max(
            ...Object.values(col.values).map((val) => val.toString().length)
          );
        }

        col.width = width + 3;
      });
    } catch (e) {
      //carry on
    }

    let workbookName =
      "StockPulse Extract " +
      new Date().getDate() +
      new Date().toLocaleString("en-gb", { month: "short" }) +
      new Date().getFullYear();

    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      fs.saveAs(blob, workbookName + ".xlsx");
    });

    this.toastService.destroyToast();
  }

  loadStockCheck(stockCheckId: number): Observable<StockCheck> {
    this.toastService.loadingToast();

    const params: DashboardTableDataParams = {
      showActive: this.stockChecksService.showActive,
      fromDate: this.stockChecksService.fromDate,
      toDate: this.stockChecksService.toDate,
    };

    let url: string = `getStockChecks?stockCheckId=${stockCheckId}&isActive=${params.showActive}`;
    if (!params.showActive) {
      url += `&fromDate=${params.fromDate}&toDate=${params.toDate}`;
    }

    return this.apiAccess.get("stockchecks", url).pipe(
      map((data: StockCheck[]) => {
        const stockCheck = data[0];
        if (stockCheck.hasSignoffImage) {
          stockCheck.signoffImageURL =
            this.constantsService.buildSignOffImageURL(stockCheckId);
        }
        this.selectionsService.stockCheck = stockCheck;
        return stockCheck;
      }),
      catchError((error) => {
        this.toastService.destroyToast();
        this.toastService.errorToast("Failed to load stock check");
        return throwError(() => error);
      })
    );
  }

  loadStockCheckAndGoToImport(
    stockCheckId: number,
    reconcilingItemTypeDescription: string
  ) {
    this.loadStockCheck(stockCheckId).subscribe({
      next: (stockCheck) => {
        if (reconcilingItemTypeDescription.split(", ")[1]) {
          const reportType: string = reconcilingItemTypeDescription.includes(
            "Missing_"
          )
            ? "Missing"
            : "Unknown";

          const description: string = `Explanations for ${reportType} Vehicles: ${
            reconcilingItemTypeDescription.split(", ")[1]
          }`;
          this.loadItemsService.chosenReconcilingItemTypeDescription =
            description;
        } else if (reconcilingItemTypeDescription === "inStock") {
          this.loadItemsService.chosenReconcilingItemTypeDescription =
            "DMS Stock";
        } else {
          this.loadItemsService.chosenReconcilingItemTypeDescription =
            reconcilingItemTypeDescription.split("_")[1];
        }

        this.router.navigateByUrl("/loadItems");
      },
      error: () => {},
    });
  }

  loadStockCheckAndGoToReconcile(
    stockCheckId: number,
    resolutionTypeDescription?: string
  ) {
    this.loadStockCheck(stockCheckId).subscribe({
      next: (stockCheck) => {
        if (resolutionTypeDescription) {
          let bar: string = resolutionTypeDescription;

          if (bar === "inStock") {
            bar = "Stock";
          } else if (bar === "inStockAndScanned") {
            bar = "In Stock and Scanned";
          } else if (bar === "scans") {
            bar = "Scanned";
          } else if (bar.includes("Auto_")) {
            bar = bar.split("_")[1];
          } else {
            bar = bar.includes("Missing")
              ? "Missing resolved"
              : "Unknown resolved";
            this.reconcileService.resolutionForFilter =
              resolutionTypeDescription.split("_")[1];
          }

          this.reconcileService.chosenBarDescription = bar;
        }

        setTimeout(() => {
          this.router.navigateByUrl("/reconcile");
        }, 100);
      },
      error: () => {},
    });
  }
}
