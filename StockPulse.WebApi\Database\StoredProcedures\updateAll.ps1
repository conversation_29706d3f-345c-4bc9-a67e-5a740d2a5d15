# Define the folder containing your SP .sql files
$sourceFolder = ".\"  
# Define the path for the combined output .sql file
$outputFile = ".\all_sps.sql" 
# Define file encoding (UTF-8 is common and good)
$fileEncoding = [System.Text.Encoding]::UTF8

# Optional: Clear the output file if it already exists
if (Test-Path $outputFile) {
    Clear-Content $outputFile
}

# Get all .sql files in the source folder.
# You might want to sort them if the order of creation/alteration matters.
# Example: Sort by name. For more complex ordering, you might need a naming convention or a list.
$sqlFiles = Get-ChildItem -Path $sourceFolder -Filter *.sql | Sort-Object Name

foreach ($file in $sqlFiles) {
    Write-Host "Processing: $($file.FullName)"

    # Add a header comment and GO for this specific file in the combined script
    Add-Content -Path $outputFile -Value "----------------------------------------------------------------------------------------------------" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "-- Start of script: $($file.Name)" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "----------------------------------------------------------------------------------------------------" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "GO" -Encoding $fileEncoding # Ensure a batch separation before this script's SET options
    Add-Content -Path $outputFile -Value "" -Encoding $fileEncoding # Add a blank line

    # Read the entire content of the current SQL file
    $content = Get-Content -Path $file.FullName -Raw -Encoding $fileEncoding

    # Add the content to the output file
    Add-Content -Path $outputFile -Value $content -Encoding $fileEncoding

    # Ensure there's a newline after the content (if the file didn't end with one)
    # And, most importantly, ensure a GO statement terminates the batch for this SP.
    # Most individual SP scripts generated by SSMS will already end with GO.
    # This ensures separation even if one was missing its final GO.
    Add-Content -Path $outputFile -Value "" -Encoding $fileEncoding # Add a blank line before the GO if content didn't end with newline
    Add-Content -Path $outputFile -Value "GO" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "----------------------------------------------------------------------------------------------------" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "-- End of script: $($file.Name)" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "----------------------------------------------------------------------------------------------------" -Encoding $fileEncoding
    Add-Content -Path $outputFile -Value "" -Encoding $fileEncoding # Add a couple of blank lines for readability
    Add-Content -Path $outputFile -Value "" -Encoding $fileEncoding
}

Write-Host "All .sql files have been combined into: $outputFile"