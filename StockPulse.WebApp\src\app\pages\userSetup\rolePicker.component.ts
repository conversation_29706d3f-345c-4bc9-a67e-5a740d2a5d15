import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import { ConstantsService } from '../../services/constants.service';
import {IconService} from '../../services/icon.service'
import { SelectionsService } from '../../services/selections.service';
import { AppUserRole } from "../../model/AppUserRole";


@Component({
    selector: 'rolePicker-cell',
    template: `
    
                <!-- Dropdown to choose new user role -->
                <div ngbDropdown container="body" id="chooseNewRole" class="d-inline-block" >
                  <button class="btn btn-primary" id="mainButton" ngbDropdownToggle>
                      {{chosenRole?.name}}

                  </button>
                  <div ngbDropdownMenu  aria-labelledby="dropdownBasic1">
                    <button (click)="chooseRole(role)" *ngFor="let role of constants.Roles"
                      ngbDropdownItem>{{role.name}}</button>
                  </div>
                </div>
  `,
    styles: [
        `
#mainButton{width:200px;}
`
    ],
    standalone: false
})
export class RolePickerCellRendererComponent implements ICellRendererAngularComp {
    params;
    chosenRole:AppUserRole

    constructor(
      public icon: IconService,
      public constants: ConstantsService,
      public selections: SelectionsService
    ) { }


    agInit(params: any): void {
     this.params = params;
     this.chosenRole = this.constants.Roles.find(x=>x.name===this.params.data.roleName)
    
    }

    chooseRole(role:AppUserRole){

      this.chosenRole = role;
      this.params.node.data.newValues.roleName = role.name;
      this.params.node.data.hasChanged = true;
    }
  

    refresh(params?:any): boolean {
        return false;
    }

   
}


