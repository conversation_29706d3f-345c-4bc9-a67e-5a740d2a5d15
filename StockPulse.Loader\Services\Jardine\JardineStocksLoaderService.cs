﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Repository.Database;
using Microsoft.IdentityModel.Tokens;
using Quartz.Util;
using StockPulse.Model.Import;

namespace StockPulse.Loader.Services
{
   public interface GenericLoaderJobServiceParams
   {
      public JobParams GetMatchingFilesAndImportParams(string incomingRoot);
      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage);
   }

   public class JardineStocksLoaderService : GenericLoaderJobServiceParams
   {

      public JardineStocksLoaderService()
      {

      }

      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "jardine";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.JardineStockItems,
            customerFolder = "jardine",
            filename = "*STK-Jardine-Stock.csv",
            importSPName = null,
            loadingTableName = "StockItems",
            jobName = "JardineStocks",
            pulse = PulsesService.STK_JardineStock,
            fileType = FileType.csv,
            regexPattern = "(?<=^|\\|)(\\\"[^\\\"]*\\\"|[^|]*)",
            headerFailColumn = "Site",
            headerDefinitions = BuildHeaderDictionary(),
            errorCount = 0,
            dealerGroupId = 7,
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), $"*STK-Jardine-Stock.csv"),
            delimiter = '|'
            // replaceStrings = new Dictionary<string, string>
            // {
            //     { ", metallic", " metallic" },
            //     { ", pearl", " pearl" },
            //     { ", crystal", " crystal" },
            //     { ", solid", " solid" },
            //     //{ "white, solid", "white solid" },
            //     //{ "black, solid", "black solid" },
            //     //{ "green, solid", "green solid" },
            //     //{ "grey, solid", "grey solid" },

            //     { "g/km,", "g/km" },
            //     { "0,00 kW", "000 kW" },
            //     { "00,00 kW", "0000 kW" },
            //     { "0,0 kW", "00 kW" },
            //     { "7,00 kW", "700 kW" },
            //     { "70,00 kW", "7000 kW" },
            //     { "6,00 kW", "600 kW" },
            //     { "10,00 kW", "1000 kW" },
            //     { "60,00 kW", "6000 kW" },
            //     { "5,00 kW", "500 kW" },
            //     { "25,00 kW", "2500 kW" },
            //     { "50,00 kW", "5000 kW" },
            //     { ",,6FXX,", ",6FXX," },
            //     { "I, M, J", "IMJ" },
            //     { "I,M,J", "IMJ" },
            //     { "I, M ,J", "IMJ" },
            //     { "GTS Coupe 3, 6", "GTS Coupe 3 6" }
            //     //{ "Blue, metallic", "Blue metallic" },
            //     //{ "blue, metallic", "blue metallic" },
            //     //{ "green, metallic", "green metallic" },

            // }
         };


         return parms;
      }

      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.StockItem> incomingStocks = new List<Model.Input.StockItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
         int incomingProcessCount = 0;
         incomingStocks = new List<Model.Input.StockItem>(10000); ;  //preset the list size (slightly quicker than growing it each time)

         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();
         Dictionary<string, int> skippedRowColsDictionary = new Dictionary<string, int>();

         using (var db = new StockpulseContext())
         {
            // Active sites for the DealerGroup
            List<Site> sites = db.Sites
                .AsNoTracking()
                .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId && s.IsActive)
                .Include(s => s.Divisions)
                .ToList();

            // Set of valid SiteIds for quick lookup
            HashSet<int> validSiteIds = sites.Select(s => s.Id).ToHashSet();

            // Only get site descriptions for primary sites with matching SiteId
            List<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary
                .Where(x => x.IsPrimarySiteId && x.DealerGroupId == parms.dealerGroupId && validSiteIds.Contains(x.SiteId))
                .AsNoTracking()
                .ToList();

            var siteDictionaryLookup = siteDescriptionDictionary.ToLookup(x => x.Description);

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               try
               {
                  if (rowCols.Length != rowsAndHeaders.headerLookup.Count())
                  {
                     //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                     string error = $"{rowCols[rowsAndHeaders.headerLookup[parms.headerFailColumn]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {rowsAndHeaders.headerLookup.Count()}";

                     if (!skippedRowColsDictionary.ContainsKey(error))
                     {
                        skippedRowColsDictionary[error] = 1;
                     }
                     else
                     {
                        skippedRowColsDictionary[error] += 1;
                     }

                     parms.errorCount++;

                     continue;
                  }

                  string siteName = rowCols[rowsAndHeaders.headerLookup["Site"]].ToUpper().Trim();

                  // Skip certain sites
                  if (SharedLoaderService.SkipSiteForJardines(siteName))
                  {
                     continue;
                  }

                  Site site = sites.Where(x => x.Description.ToUpper() == siteName).FirstOrDefault();

                  int siteId;

                  if (site == null)
                  {
                     // Use the lookup for fast lookups
                     var siteDictionary = siteDictionaryLookup
                         .FirstOrDefault(g => g.Key.ToUpper() == siteName.ToUpper())?
                         .FirstOrDefault();

                     if (siteDictionary == null)
                     {
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }

                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }
                     else
                     {
                        siteId = siteDictionary.SiteId;
                     }
                  }
                  else
                  {
                     siteId = site.Id;
                  }

                  var reg = GetValue(rowCols, rowsAndHeaders, "RegNo");
                  var vin = GetValue(rowCols, rowsAndHeaders, "VIN");
                  var description = GetValue(rowCols, rowsAndHeaders, "Description");
                  var branch = GetValue(rowCols, rowsAndHeaders, "Branch");

                  var dis = GetNullableIntValue(rowCols, rowsAndHeaders, "DIS");
                  var groupDis = GetNullableIntValue(rowCols, rowsAndHeaders, "GIS");
                  var comment = GetValue(rowCols, rowsAndHeaders, "Comment");
                  var stockType = GetValue(rowCols, rowsAndHeaders, "StockType");
                  var reference = GetValue(rowCols, rowsAndHeaders, "Stock_Vehicle_Key");
                  var stockValue = GetDecimalValue(rowCols, rowsAndHeaders, "SIV");

                  var dealerGroupId = parms.dealerGroupId;
                  var fileImportId = parms.fileImportId;
                  const int sourceReportId = 1;

                  // If no VIN and no reg, but we have reference - use Reference as VIN
                  if (vin.IsNullOrEmpty() && reg.IsNullOrEmpty() && !reference.IsNullOrEmpty())
                  {
                     vin = reference;
                  }

                  // If no reg or vin or reference, set VIN to be row count
                  if (reg.IsNullOrEmpty() && vin.IsNullOrEmpty() && reference.IsNullOrEmpty())
                  {
                     vin = (incomingProcessCount + 1).ToString();
                  }

                  // Now create the object
                  Model.Input.StockItem jardineStock = new Model.Input.StockItem()
                  {
                     SiteId = siteId,
                     Reg = reg?.Replace(" ", "") ?? "",
                     Vin = vin,
                     Description = description,
                     DIS = dis,
                     GroupDIS = groupDis,
                     Comment = comment,
                     StockType = stockType,
                     Reference = reference,
                     StockValue = stockValue,
                     DealerGroupId = dealerGroupId,
                     Branch = branch,
                     FileImportId = fileImportId,
                     SourceReportId = sourceReportId
                  };

                  incomingStocks.Add(jardineStock);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         // Skipped row errors summarised
         skippedRowColsDictionary = skippedRowColsDictionary
            .OrderBy(kvp => kvp.Key) 
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in skippedRowColsDictionary)
         {
            logMessage.FailNotes += $"InterpretFile: {item.Key} ({item.Value}) <br>";
         }

         // Missing sites errors summarised 
         missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingStocks.ToDataTable();

         result.Columns.Remove("SourceReports");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("Sites");
         result.Columns.Remove("DealerGroup");

         return result;
      }

      // ------------------ Helper Methods ------------------
      private string GetValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         return rowsAndHeaders.headerLookup.ContainsKey(key)
             ? rowCols[rowsAndHeaders.headerLookup[key]]
             : null;
      }

      private int? GetNullableIntValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return null;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return int.TryParse(value, out var result) ? result : (int?)null;
      }

      private decimal GetDecimalValue(string[] rowCols, dynamic rowsAndHeaders, string key)
      {
         if (!rowsAndHeaders.headerLookup.ContainsKey(key)) return 0m;

         var value = rowCols[rowsAndHeaders.headerLookup[key]];
         return decimal.TryParse(value, out var result) ? result : 0m;
      }


      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
            {
                { "Site", "SITE" },
                { "Branch_Check", "BRANCH_CHECK" },
                { "Current_Location", "CURRENT_LOCATION" },
                { "Division", "DIVISION" },
                { "Enterprise", "ENTERPRISE" },
                { "RegNo", "REGNO" },
                { "VIN", "VIN" },
                { "Description", "DESCRIPTION" },
                { "In_Stock_date", "IN_STOCK_DATE" },
                { "Group_In_Stock_Date", "GROUP_IN_STOCK_DATE" },
                { "DIS", "DIS" },
                { "GIS", "GIS" },
                { "Branch", "BRANCH" },
                { "Comment", "COMMENT" },
                { "Status", "STATUS" },
                { "Profile", "PROFILE" },
                { "StockType", "STOCKTYPE" },
                { "Stock_Vehicle_KEY", "STOCK_VEHICLE_KEY" },
                { "Stock_Number", "STOCK_NUMBER" },
                { "SIV", "SIV" }
            };

         return headerDefinitions;
      }

   }
}
