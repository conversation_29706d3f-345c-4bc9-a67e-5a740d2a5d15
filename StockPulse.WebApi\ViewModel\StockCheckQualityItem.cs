﻿using System;

namespace StockPulse.WebApi.ViewModel
{
   public class StockCheckQualityItem
   {
      public int StockCheckId { get; set; }
      public string Site { get; set; }
      public string Division { get; set; }
      public DateTime StockCheckDate { get; set; }
      public string Status { get; set; }
      public string CompletedBy { get; set; }
      public DateTime LastUpdated { get; set; }
      
      
      public int StockItems { get; set; }  //ok
      public int Scans { get; set; } //ok
      public int ScannedInStock { get; set; }   //ok
      public double InStockPercent { get => Scans == 0 ? 0 : (double)ScannedInStock / (double)Scans; }

      public int DuplicateStockItems { get; set; } //ok
      public int DuplicateScans { get; set; } //ok
       
      //Missings
      public int MissingsAutoResolved { get; set; }  //ok
      public int MissingsManuallyResolved { get; set; } //ok
      public double MissingsResolvedPct { get => 
               MissingsTotal == 0 ? 
               1 : 
            ((MissingsAutoResolved + MissingsManuallyResolved) / (double)MissingsTotal); }
      
      public int MissingsUnresolved { get; set; } //ok
      public double MissingsUnresolvedPct { get => MissingsTotal == 0 ?
               0 :
            (MissingsUnresolved/ (double)MissingsTotal);
      }
      public int MissingsTotal { get=> MissingsAutoResolved + MissingsManuallyResolved + MissingsUnresolved; }



      //Unknowns
      public int UnknownsAutoResolved { get; set; } //ok
      public int UnknownsManuallyResolved { get; set; } //ok
      public double UnknownsResolvedPct
      {
         get =>
              UnknownsTotal == 0 ?
              1 :
           ((UnknownsAutoResolved + UnknownsManuallyResolved) / (double)UnknownsTotal);
      }
      public int UnknownsUnresolved { get; set; }  //ok
      public double UnknownsUnresolvedPct
      {
         get => UnknownsTotal == 0 ?
               0 :
            (UnknownsUnresolved / (double)UnknownsTotal);
      }
      public int UnknownsTotal { get => UnknownsAutoResolved + UnknownsManuallyResolved + UnknownsUnresolved; }



      //Total
      public int TotalMissingAndUnknown { get => MissingsTotal + UnknownsTotal; }
      public int TotalMissingAndUnknownResolved { get => MissingsAutoResolved + UnknownsAutoResolved + MissingsManuallyResolved + UnknownsManuallyResolved; }
      public double TotalMissingAndUnknownResolvedPct { get=> TotalMissingAndUnknown == 0 ? 1 : (double)TotalMissingAndUnknownResolved / TotalMissingAndUnknown; }
      public int TotalAutoReconciled { get => MissingsAutoResolved + UnknownsAutoResolved; }
      public double TotalAutoReconciledPct { get => TotalMissingAndUnknown == 0 ? 1 : (double)TotalAutoReconciled / TotalMissingAndUnknown; }
      

      //Edited
      public int RegsEdited { get; set; }  //ok
      public int VinsEdited { get; set; } //ok
      public int TotalEdited { get => RegsEdited + VinsEdited; }
      public double TotalEditedPct { get => Scans == 0 ? 0 : (double)(RegsEdited + VinsEdited) / Scans; }
      
   }
}
