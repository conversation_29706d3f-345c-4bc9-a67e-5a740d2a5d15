import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { CphPipe } from '../../cph.pipe';
import { UserAndLogin } from "../../model/UserAndLogin";
import { ConstantsService } from '../../services/constants.service';
import { GetDataService } from '../../services/getData.service';
import { SaveDataService } from '../../services/saveData.service';
import { SelectionsService } from '../../services/selections.service';
import { AppUserRole } from "../../model/AppUserRole";
import { SiteVMWithSelected } from "../../model/SiteVMWithSelected";


@Component({
    selector: 'addUserModal',
    template: `
    
  <ng-template #modalRef let-modal>
      <div class="modal-header">
       <h4 *ngIf="!isExistingUser"> Create New User </h4>
       <h4 *ngIf="isExistingUser"> Update User </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body alertModalBody lowHeight">

        <div class="contentTile">
        
        
        <table class="cph fullWidth">

            <tbody>

              <tr *ngIf="userIsLockedOut">
                <td class="userLockedOutMessage" colspan="2">
                  <span class="text-danger">
                    The user already has a StockPulse account which has been locked. {{ newUser.name }} can go to stockpulse.cphi.co.uk/forgotpassword to
                    request a new password and regain access to their account.
                  </span>
                </td>
              </tr>

                <!-- Name -->
                <!-- <tr>
                <td class="rowHeader">Code</td>
                <td>
                  <input type="number" class="costing"  [(ngModel)]="newUser.code" (ngModelChange)="checkOkToSave()" />
              </tr> -->
                <!-- Name -->
                <tr>
                <td class="rowHeader">Name <span class="requiredField">*</span></td>
                <td>
                  <input class="costing"  [(ngModel)]="newUser.name" (ngModelChange)="checkOkToSave()" ngbAutofocus />
              </tr>
               

               <!-- Email -->
               <tr>
                <td class="rowHeader">Email <span class="requiredField">*</span></td>
                <td>
                  <div class="spaceBetween column emailSection">
                  <input class="costing"  [ngbPopover]="emailProblem"  
                    triggers="manual"  popoverClass="smallPopover newUserProblem" popoverTitle="Please check" placement="auto" #emailPopover="ngbPopover"
                  #email="ngModel" (blur)="isInvalidEmail(newUser.email); isTakenEmail(newUser.email); emailBoxTouched = true; emailProblems() ? emailPopover.open() : emailPopover.close()"  
                  [ngClass]="{'invalid':invalidEmail && emailBoxTouched}" 
                  [(ngModel)]="newUser.email" (ngModelChange)="checkOkToSave()" />
                        
                    </div>
              </tr>
               
              <tr>
                <td class="rowHeader">Employee Number</td>
                <td>
                  <input class="costing"  [(ngModel)]="newUser.employeeNumber" />
              </tr>
              
               <!-- Role -->
               <tr>
                <td class="rowHeader">Role <span class="requiredField">*</span></td>
                <td class="alignRight">

                 <!-- Dropdown to choose new user role -->
                <div ngbDropdown container="body" id="chooseNewRole" class="d-inline-block" >
                  <button class="btn btn-primary d-flex align-items-center justify-content-between" ngbDropdownToggle>
                      <span *ngIf="newUser.roleName"> {{newUser.roleName}} </span>
                      <span *ngIf="!newUser.roleName"> Choose Role </span>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button (click)="chooseRole(role)" *ngFor="let role of constants.Roles"
                      ngbDropdownItem>{{role.name}}</button>
                  </div>
                </div>
                </td>
                 
              </tr>


               
                <!-- Site -->
                <tr>
                <td class="rowHeader">Home Site <span class="requiredField">*</span></td>
                <td class="alignRight">
                  <!-- Site dropdown -->
                <div ngbDropdown container="body" id="chooseNewSite" class="d-inline-block" >
                  <button class="btn btn-primary d-flex align-items-center justify-content-between" ngbDropdownToggle>
                      <span *ngIf="chosenSite">{{chosenSite.description}}</span>
                      <span *ngIf="!chosenSite">Choose Site</span>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <input type="text" placeholder="Search..." [(ngModel)]="searchString"
                      (ngModelChange)="searchList()">
                    <button (click)="chooseSite(site)" *ngFor="let site of sitesCopy"
                      ngbDropdownItem>
                      {{site.description}} <span *ngIf="!site.isActive"> (Inactive)</span>
                    </button>
                  </div>
                </div>
                </td>
              </tr>


              <!-- Sites -->
                <tr>
                <td class="rowHeader">Sites <span class="requiredField">*</span></td>
                <td class="alignRight">

               <!-- Site selector -->
                <sitePicker [width]="250" [sitesFromParent]="chosenSites" [buttonClass]="''" [showInactiveSites]="true"
                  (updateSites)="onUpdateSites($event)"></sitePicker>
                  </td>
              </tr>

       
            </tbody>
        </table>

        </div>
      </div>

      <div class="modal-footer">
        <!-- <button type="button" class="btn btn-primary" (click)="modal.close()">OK</button> -->
        <button *ngIf="!isExistingUser" [disabled]="!isOkToSave" class="btn btn-success" (click)="saveNewUser()">Save New User</button>
        <button *ngIf="isExistingUser" [disabled]="!isOkToSave" class="btn btn-success" (click)="saveUser()">Save User</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
      </div>
</ng-template>

<ng-template #emailProblem>

    <div class="emailProblem" >
    <div class="redFont" *ngIf="invalidEmail && emailBoxTouched">Please check the email address </div>
      <div class="redFont" *ngIf="takenEmail && emailBoxTouched">Sorry that email is taken </div>
        </div>

</ng-template>

<ng-template #usernameProblem>

<div class="usernameProblem" >
    <div class="redFont" *ngIf="invalidUsername && usernameBoxTouched">Please check the username format it should be first.last </div>
      <div class="redFont" *ngIf="takenUserName && usernameBoxTouched">Sorry that username is taken </div>
        </div>
</ng-template>

    
   `,
    styles: [`

  .redFont{color:red;}
  .emailSection{align-items: flex-end;}

  #chooseNewSite > button, #chooseNewRole > button{width:250px}
  .rowHeader {width: 15em;}
  .contentTile{width: 95%;background:white;    padding: 1em 1em;}

  table{margin: 3em 0em 10em 0em;    width: 50em;    margin: 4em auto;}
  /* table tr td:nth-of-type(2){display: flex;
    justify-content: flex-end;} */

  #costingsHolder{width:100%;    text-align: left;}
  .costing{
    border-radius: 0em;
    background: white;
    padding: 0em 1em;
    display: inline-block;
    margin: 0.2em;
    height: 3em;
    width:30em;
    }

    table input{border: 1px solid var(--grey80);text-align:left!important;}

    table td.alignRight{text-align:right!important}
    
    .requiredField {
      color: red;
    }

    .userLockedOutMessage {
      line-height: initial;
      padding-bottom: 1em;
    }
    `],
    standalone: false
})


export class NewUserModalComponent implements OnInit {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;

  newUser: UserAndLogin;
  isOkToSave: boolean;
  formattedNewProdPieceValue: any;
  formattedNewMassValue: any;
  chosenSite:SiteVMWithSelected;
  chosenSites:SiteVMWithSelected[]
  allSites:SiteVMWithSelected[]
  invalidEmail:boolean;
  takenEmail:boolean;
  emailBoxTouched: boolean;
  usernameBoxTouched:boolean;
  invalidUsername: boolean;
  takenUserName: boolean;
  userIsLockedOut: boolean;
  isExistingUser: boolean;
  sitesCopy: SiteVMWithSelected[];
  searchString: string = '';
  currentUserEmail: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public data: GetDataService,
    public saveData: SaveDataService,
    public cphPipe: CphPipe,
    public apiAccess: ApiAccessService,
    public toastService: ToastService
  ) { }


  ngOnInit(): void {
  }



  isInvalidUsername(username:string){
    this.invalidUsername = ! /^[A-Za-z]+\.([A-Za-z]\.)?[A-Za-z0-9]+$/.test(username)
    return this.invalidUsername
  }

  isInvalidEmail(email:string){
    this.invalidEmail = ! /^.+@.+\..+$/.test(email);

     return this.invalidEmail
  }

  isTakenEmail(email:string){
    let existingEmails = this.selections.userSetup.rowData.map(x=>x.email)
    this.takenEmail = existingEmails.includes(email)
    return this.takenEmail
  }

  emailProblems(){
      let invalid = this.isInvalidEmail(this.newUser.email);
      let taken = this.isTakenEmail(this.newUser.email);
      return (invalid || taken);
  }

  usernameProblems(){
    let invalid = this.isInvalidUsername(this.newUser.userName);
     let taken = this.isTakenUsername();
      return (invalid || taken);
  }

  isTakenUsername(){
    let existingUsernames = this.selections.userSetup.rowData.map(x=>x.userName);
    this.takenUserName = existingUsernames.includes(this.newUser.userName)
    return this.takenUserName
  }

  initiateNewUser() {
    this.isExistingUser = false;
    this.sitesCopy = JSON.parse(JSON.stringify(this.constants.Sites));
    this.isOkToSave = false;
    this.formattedNewProdPieceValue = '0';
    this.newUser = {
      code:null,
      name: null,
      nameShort: null,
      userName: null,
      roleName: null,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
      email: null,
      sites: null,
      siteCode: null,
      hasChanged: false,
      employeeNumber: null,
      isDeleted: false,
      isLocked: false
    }
    this.chosenSite = null;
    this.chosenSites = [];
  }

  initiateExistingUser(userDetails) {
    this.isExistingUser = true;
    this.sitesCopy = JSON.parse(JSON.stringify(this.constants.Sites));
    this.isOkToSave = false;
    this.formattedNewProdPieceValue = '0';
    this.newUser = {
      appUserId: userDetails.appUserId,
      code: userDetails.code,
      name: userDetails.name,
      nameShort: userDetails.nameShort,
      userName: userDetails.email,
      roleName: userDetails.roleName,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
      email: userDetails.email,
      sites: userDetails.sites,
      siteCode: userDetails.siteCode,
      hasChanged: false,
      employeeNumber: userDetails.employeeNumber,
      isDeleted: userDetails.isDeleted,
      isLocked: userDetails.isLocked
    }
    this.currentUserEmail = this.constants.clone(userDetails.email);
    this.chosenSite = this.constants.Sites.find(site => site.id === userDetails.siteCode);
    this.chosenSites = this.constants.Sites.filter(x => userDetails.sites.split(',').includes(x.id.toString()));
  }

  chooseSite(site:SiteVMWithSelected){
    this.searchString = '';
    this.sitesCopy = JSON.parse(JSON.stringify(this.constants.Sites));
    this.chosenSite = site;
    this.newUser.siteCode = site.id;
    this.checkOkToSave()
  }

  resetModal(){
    // this.chosenSite = null;
    // this.chosenSites = [];
    this.invalidEmail = false;
    this.takenEmail = false;
    this.emailBoxTouched = false;
    this.userIsLockedOut = false;
  }



  showModal() {

    this.resetModal()

    this.allSites = this.constants.clone(this.constants.Sites)
    this.allSites.map(x=>x.isSelected = false)

    this.modalService.open(this.modalRef, { windowClass: "confirmModal", keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //'ok'

    }, (reason) => {
      //cancel
    });
  }



  chooseRole(role:AppUserRole){
    this.newUser.roleName = role.name
    this.checkOkToSave()
  }

 

  onUpdateSites(sites:SiteVMWithSelected[]){
    let siteIds:number[] = sites.map(x=>x.id)
    this.newUser.sites = siteIds.join(',')
    this.chosenSites = sites
    this.checkOkToSave()
  }

  


  checkOkToSave() {
    this.isOkToSave = !!(!!this.newUser.roleName &&
      !!this.newUser.name &&
      !!this.newUser.email &&
      this.isExistingUser ? (this.currentUserEmail === this.newUser.email || !this.isTakenEmail(this.newUser.email)) : !this.isTakenEmail(this.newUser.email) &&
      !this.isInvalidEmail(this.newUser.email) &&
      !!this.newUser.sites &&
      !!this.newUser.siteCode
      )

  }




  saveNewUser() {
    this.toastService.loadingToast('Creating user...');
    
    this.newUser.userName = this.newUser.email;

    this.apiAccess.saveNewUser(this.newUser).subscribe((item) => {
      //gives item back
      this.newUser.code = item.code;
      this.newUser.appUserId = item.appUserId;
      this.selections.newUserAndLoginEmitter.next(this.newUser)
      this.modalService.dismissAll();

    }, e => {
      this.toastService.destroyToast();
      if (e.message === 'User with email already exists') {
        this.toastService.errorToast(e.message);
        this.userIsLockedOut = true;
      } else {
        this.toastService.errorToast('Failed to create user');
      }
    },()=>{
      //finished
      this.toastService.destroyToast();
      this.toastService.successToast('Successfully created user');
    })

  }

  saveUser() {
    this.toastService.loadingToast('Updating user...');
    
    // this.newUser.userName = this.newUser.email;

    this.apiAccess.patch('User', 'UserAndLogin', this.newUser).subscribe((item) => {
      //gives item back
      this.selections.newUserAndLoginEmitter.next(this.newUser)
      this.modalService.dismissAll();

    }, e => {
      this.toastService.destroyToast();
      this.toastService.errorToast('Failed to update user');
    },()=>{
      //finished
      this.toastService.destroyToast();
      this.toastService.successToast('Successfully updated user');
    })
  }

  searchList() {
    let sitesCopy: SiteVMWithSelected[] = JSON.parse(JSON.stringify(this.constants.Sites));
    this.sitesCopy = sitesCopy.filter(site => site.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }

}


