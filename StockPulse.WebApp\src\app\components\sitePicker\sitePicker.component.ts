import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { SiteVMWithSelected } from "../../model/SiteVMWithSelected";
import { IconService } from "../../services/icon.service";
import { ConstantsService } from './../../services/constants.service';

@Component({
    selector: 'sitePicker',
    templateUrl: './sitePicker.component.html',
    styleUrls: ['./sitePicker.component.scss'],
    standalone: false
})
export class SitePickerComponent implements OnInit {
  @Input() sitesFromParent: SiteVMWithSelected[];
  @Input() width: number;
  @Input() disabled: boolean;
  @Input() showInactiveSites: boolean;
  @Output() updateSites = new EventEmitter<SiteVMWithSelected[]>();

  sites: SiteVMWithSelected[];
  sitesCopy: SiteVMWithSelected[];
  siteIds: number[];
  label: string;
  allSitesSelected: boolean;
  copyOfSitesFromParent: SiteVMWithSelected[];
  searchString: string = '';

  constructor(
    public constants: ConstantsService,
    public icon: IconService
  ) { }

  ngOnInit(): void {
    this.copyOfSitesFromParent = this.constants.clone(this.sitesFromParent);
    this.siteChosenLabel();
  }

  siteChosenLabel() {
    if (!this.sitesFromParent) return this.label = 'Sites';
    if (this.sitesFromParent.length === 0) return this.label = 'No Sites selected';
    if (this.sitesFromParent.length === 1) return this.label = this.sitesFromParent[0].description;
    if (this.sitesFromParent.length < 4) {
      let siteNames: string = '';
      this.sitesFromParent.forEach((site, i) => {
        if (i > 0) siteNames = siteNames + ',';
        siteNames = siteNames + site.description;
      })
      return this.label = siteNames;
    }
    
    if (this.sitesFromParent.length === this.constants.Sites.length) return this.label = 'All Sites ';

    // More than 3 sites selected
    return this.label = this.sitesFromParent.length + ' Sites';
  }

  generateSitesList() {
    this.siteIds = this.sitesFromParent.map(x => x.id);
    this.sites = JSON.parse(JSON.stringify(this.showInactiveSites ? this.constants.Sites : this.constants.SitesActive));
    
    this.sites.forEach(s => {
      if (this.siteIds.indexOf(s.id) > -1) s.isSelected = true;
    })

    this.sitesCopy = JSON.parse(JSON.stringify(this.sites));
  }

  toggleItem(item: SiteVMWithSelected) {
    item.isSelected = !item.isSelected;
    this.sitesCopy.forEach(site => {
      if (site.id === item.id) {
        site.isSelected = item.isSelected;
      }
    })

    this.sitesFromParent = this.sitesCopy.filter(x => x.isSelected);
    this.siteChosenLabel();
  }

  selectSites() {
    this.updateSites.emit(this.sitesCopy.filter(e => e.isSelected));
  }

  toggleSites() {
    if (this.sitesCopy.filter(x => x.isSelected).length === this.sites.length) {
      this.allSitesSelected = false;

      this.sites.forEach(s => {
        s.isSelected = false;
      })

      this.sitesCopy.forEach(s => {
        s.isSelected = false;
      })
    } else {
      this.allSitesSelected = true;

      this.sites.forEach(s => {
        s.isSelected = true;
      })

      this.sitesCopy.forEach(s => {
        s.isSelected = true;
      })
    }

    this.sitesFromParent = this.sitesCopy.filter(x => x.isSelected);
    this.siteChosenLabel();
  }

  clearSelection() {
    this.sitesFromParent = this.constants.clone(this.copyOfSitesFromParent);
    this.siteChosenLabel();
  }

  searchList() {
    let sitesCopy: SiteVMWithSelected[] = JSON.parse(JSON.stringify(this.sitesCopy));
    this.sites = sitesCopy.filter(site => site.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }
}
