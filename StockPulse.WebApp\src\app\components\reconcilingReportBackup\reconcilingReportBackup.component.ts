import { Component, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImageToUpdate } from 'src/app/model/ImageToUpdate';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { FileWithBase64 } from '../vehicleModal/vehicleModalBodyNew/explanation/explanation.component';

@Component({
    selector: 'app-reconciling-report-backup',
    templateUrl: './reconcilingReportBackup.component.html',
    styleUrls: ['./reconcilingReportBackup.component.scss'],
    standalone: false
})
export class ReconcilingReportBackupComponent implements OnInit {
  stockCheckId: number;
  reconcilingItemTypeId: number;
  existingBackupsFiles: ImageToUpdate[];
  fileSizeExceeded: boolean;
  resolutionFiles: FileWithBase64[] = [];

  constructor(
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public iconService: IconService,
    public toastService: ToastService,
    public apiAccessService: ApiAccessService,
    public selectionsService: SelectionsService
  ) {

  }

  ngOnInit(): void {
    this.getExistingBackups();
  }

  getExistingBackups() {
    const params = [
      { key: 'stockCheckId', value: this.stockCheckId },
      { key: 'reconcilingItemTypeId', value: this.reconcilingItemTypeId }
    ]

    this.apiAccessService.get('ReconcilingItems', 'GetBackups', params).subscribe((res: ImageToUpdate[]) => {
      this.existingBackupsFiles = res;
    }, error => {
      console.error('Failed to retrieve backups', error);
    });
  }

  public onCancelButtonClick() {
    this.activeModal.dismiss();
  }

  onFileDropped(event: File[]) {
    this.prepareFilesList(event);
  }

  onFilePaste(event: ClipboardEvent) {
    event.preventDefault();
    let file: File = event.clipboardData.items[0].getAsFile();
    this.prepareFilesList([file]);
  }

  fileBrowseHandler(event: any) {
    const files: File[] = event.target.files;
    this.prepareFilesList(files);
  }

  prepareFilesList(files: File[]) {
    for (let item of files) {
      if (item.size > 10000000) { // 10mb limit
        this.fileSizeExceeded = true;
      } else {
        let reader: FileReader = new FileReader();
        reader.onload = (e) => {
          let base64: string = e.target.result as string;
          let object: FileWithBase64 = Object.assign(item, { base64: base64 });
          this.resolutionFiles.push(object);
        }
        reader.readAsDataURL(item);
      }
    }
  }

  formatBytes(bytes: number, decimals: number = 2) {
    if (bytes === 0) { return "0 Bytes"; }

    const k: number = 1024;
    const dm: number = decimals <= 0 ? 0 : decimals;
    const sizes: string[] = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const i: number = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  }

  deleteFile(index: number) {
    this.resolutionFiles.splice(index, 1);
  }

  convertFilesForTransfer(): ImageToUpdate[] {
    const files: ImageToUpdate[] = [];
    this.resolutionFiles.forEach(file => {
      files.push({ status: "ADD", fileBase64: file.base64, id: null, url: '', fileName: file.name });
    })

    return files;
  }

  thereAreFiles() {
    if (this.resolutionFiles && this.resolutionFiles.length > 0) { return true; }
    return false;
  }

  getThumbnail(file: any) {
    if (file.base64.includes('data:image')) {
      return file.base64;
    } else {
      return this.imageForFileExtensions(file.name);
    }
  }

  updateUrl(file: ImageToUpdate) {
    let imageElement: HTMLImageElement = document.getElementById(`backup-image-${file.id}`) as HTMLImageElement;
    imageElement.src = this.imageForFileExtensions(file.fileName);
  }

  imageForFileExtensions(nameOrSrc: string) {
    if (nameOrSrc.includes('.pdf')) {
      return './assets/imgs/backup-pdf.png';
    } else if (nameOrSrc.includes('.docx')) {
      return './assets/imgs/backup-word.png';
    } else if (nameOrSrc.includes('.xl') || nameOrSrc.includes('.csv')) {
      return './assets/imgs/backup-excel.png';
    } else {
      return './assets/imgs/backup-file.png';
    }
  }

  saveBackupFiles() {
    const savingToast = this.toastService.loadingToast('Saving backup...');
    const files: ImageToUpdate[] = this.convertFilesForTransfer();
    const params = {
      Files: files,
      StockCheckId: this.stockCheckId,
      ReconcilingItemTypeId: this.reconcilingItemTypeId
    }

    this.apiAccessService.post('ReconcilingItems', 'SaveBackup', params).subscribe((data: any) => {
      this.toastService.successToast('Backup saved');
      this.resolutionFiles = [];
      this.getExistingBackups();
      savingToast.close();
    }, error => {
      this.toastService.errorToast('Failed to save backup');
      console.error('Failed to save backup', error);
      savingToast.close();
    });
  }

  downloadFile(file: ImageToUpdate) {
    let link = document.createElement("a");
    link.href = file.url;
    link.download = file.fileName;
    link.click();
  }

  deleteBackup(file: ImageToUpdate) {
    const savingToast = this.toastService.loadingToast('Deleting backup...');

    file.status = "DELETE";

    const params = {
      Files: [file],
      StockCheckId: this.stockCheckId,
      ReconcilingItemTypeId: this.reconcilingItemTypeId
    }

    this.apiAccessService.post('ReconcilingItems', 'SaveBackup', params).subscribe((data: any) => {
      this.toastService.successToast('Backup deleted');
      this.existingBackupsFiles = this.existingBackupsFiles.filter(x => x.id !== file.id);
      savingToast.close();
    }, error => {
      this.toastService.errorToast('Failed to delete backup');
      console.error('Failed to delete backup', error);
      savingToast.close();
    });
  }
}
