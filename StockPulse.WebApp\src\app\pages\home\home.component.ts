import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AppStartService } from 'src/app/services/appStart.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockChecksService } from '../stockChecks/stockChecks.service';

@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    standalone: false
})
export class HomeComponent implements OnInit {
  @ViewChild('modal', { static: true }) modal: NgbModal;


  constructor(
    public appStartService: AppStartService,
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public router: Router,
    public modalService: NgbModal,
    public toastService: ToastService,
    private stockchecksService:StockChecksService
  ) { }

  ngOnInit(): void {
    this.toastService.destroyToast();

    if(!this.selectionsService.stockCheck){
      this.router.navigateByUrl('/load')
    }
    
  }

  goTo(pageName: string) {
    this.toastService.loadingToast();

    let pagesNotRequiringStockcheckLoaded: string[] = [
      'stockChecks', 'userMaintenance', 'vehicleSearch', 'dashboard'
    ]

    if (!pagesNotRequiringStockcheckLoaded.includes(pageName) && !this.selectionsService.stockCheck) {
      this.modalService.open(this.modal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        this.router.navigateByUrl('stockChecks');
      }, (reason) => {
        // Modal dismissed
      })
    } else {
      this.router.navigateByUrl(pageName);
    }
  }

  getUserFirstName() {
    let name: string = this.selectionsService.usersName;
    return name.split(" ")[0];
  }

  loadStockCheck3383(){
    this.stockchecksService.getAndSetStockChecks(false,true).subscribe(res=>{
      const stockCheck3383 = this.stockchecksService.stockCheckVMs.find(x=>x.id==3383);
      this.stockchecksService.loadStockCheck(stockCheck3383,false);
    })
  }

  showLoad3383(){
    return window.location.hostname==='localhost'
  }

  showUserMaintenance(){
    return this.constantsService.userRolePermissions.showUserMaintenance;
  }

  
  showTableMaintenance(){
    return this.constantsService.userRolePermissions.showTableMaintenance && (this.selectionsService.userUsername.endsWith('@cphi.co.uk') || this.selectionsService.userUsername.endsWith('@cphinsight.com'))
  }

  showRegVinMismatch() {
    return !this.constantsService.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue;
  }
}
