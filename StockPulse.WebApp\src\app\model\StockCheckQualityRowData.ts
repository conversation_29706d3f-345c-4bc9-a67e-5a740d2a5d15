export interface StockCheckQualityRowData {
   StockCheckId: number;
   Site: string;
   Division: string;
   StockCheckDate: Date | string;
   Status: string;
   CompletedBy: string;
   LastUpdated: Date | string;
   StockItems: number;
   Scans: number;
   ScannedInStock: number;
   InStockPercent: number;

   DuplicateStockItems: number;
   DuplicateScans: number;

   //Missings
   MissingsAutoResolved: number;
   MissingsManuallyResolved: number;
   MissingsResolvedPct: number;

   MissingsUnresolved: number;
   MissingsUnresolvedPct: number;
   MissingsTotal: number;

   //Unknowns
   UnknownsAutoResolved: number;
   UnknownsManuallyResolved: number;
   UnknownsResolvedPct: number;
   UnknownsUnresolved: number;
   UnknownsUnresolvedPct: number;
   UnknownsTotal: number;

   //Total
   TotalMissingAndUnknown:number;
   TotalMissingAndUnknownResolved:number;
   TotalMissingAndUnknownResolvedPct:number
   TotalAutoReconciled: number;
   TotalAutoReconciledPct: number;

   //Edited
   RegsEdited: number;
   VinsEdited: number;
   TotalEdited: number;
   TotalEditedPct: number;
}
