import { Injectable } from "@angular/core";
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from "@angular/common/http";
import { Observable, from } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ConstantsService } from "./services/constants.service";

import version from "./../../package.json";
import { AzureAdSSOService } from "./services/azure-ad-sso.service";

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  constructor(
    private constants: ConstantsService,
    private azureAdSSOService: AzureAdSSOService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    return from(this.azureAdSSOService.getAccessToken(false)).pipe(
      switchMap((token: string | null) => {
        const clonedRequest = request.clone({
          setHeaders: {
            Authorization: `Bearer ${token ?? ""}`,
            WebVersion: version.version,
            DealerGroupName: this.constants.getDealerGroupName(),
          },
        });

        return next.handle(clonedRequest);
      })
    );
  }

  
}

