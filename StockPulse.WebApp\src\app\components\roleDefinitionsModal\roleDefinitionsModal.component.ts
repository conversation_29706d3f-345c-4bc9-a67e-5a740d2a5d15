import { Component, ElementRef, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ConstantsService } from "src/app/services/constants.service";
import { IconService } from "src/app/services/icon.service";

@Component({
  selector: "app-roleDefinitionsModal",
  templateUrl: "./roleDefinitionsModal.component.html",
  styleUrls: ["./roleDefinitionsModal.component.scss"],
  standalone: false
})
export class RoleDefinitionsModalComponent {
  @ViewChild("modalRef", { static: true }) modalRef: ElementRef;
  permissionKeys: string[];

  constructor(
    public modalService: NgbModal,
    public constantsService: ConstantsService,
    public iconService: IconService
  ) {}

  showModal() {
    this.permissionKeys = Object.keys(
      this.constantsService.rolePermissions[0]
    ).filter((key) => key !== "role");

    this.modalService
      .open(this.modalRef, {
        size: "xl",
        keyboard: true,
        ariaLabelledBy: "modal-basic-title",
      })
      .result.then(
        (result) => {},
        (reason) => {}
      );
  }
}
