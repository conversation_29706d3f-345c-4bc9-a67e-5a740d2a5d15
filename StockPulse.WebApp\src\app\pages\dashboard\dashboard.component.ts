import { Component, OnInit, ViewChild } from "@angular/core";
import { DashboardService } from "./dashboard.service";
import { interval, Subscription } from "rxjs";
import { takeWhile } from "rxjs/operators";
import { ToastService } from "src/app/services/newToast.service";
import { LogoService } from "src/app/services/logo.service";
import { TimePeriod } from "src/app/model/TimePeriod";
import { HeatmapComponent } from "./components/heatmap/heatmap.component";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
  selector: "app-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
})
export class DashboardComponent implements OnInit {
  @ViewChild("heatmap") heatmap?: HeatmapComponent;

  // private alive: boolean = true;
  private refreshPageSubscription: Subscription;

  get totalScansByHour() {
    return this.constantsService.sum(
      this.dashboardService.dashboard.scansByHourItems.map((x) => x.count)
    );
  }

  constructor(
    public dashboardService: DashboardService,
    public toastService: ToastService,
    public logoService: LogoService,
    public constantsService: ConstantsService
  ) {}

  ngOnInit(): void {
    this.dashboardService.getData();
    // this.getDataAtInterval();

    this.refreshPageSubscription = this.constantsService.refreshPage.subscribe(
      (res) => {
        if (res) {
          this.toastService.loadingToast("Refreshing...");
          this.dashboardService.getData(true);
        }
      }
    );
  }

  // getDataAtInterval(): void {
  //   interval(60 * 1000)
  //     .pipe(takeWhile(() => this.alive))
  //     .subscribe(() => {
  //       this.dashboardService.getData();
  //     });
  // }

  ngOnDestroy(): void {
    // this.alive = false;

    if (this.refreshPageSubscription) {
      this.refreshPageSubscription.unsubscribe();
    }
  }

  chooseHeatmapTimePeriod(period: TimePeriod) {
    this.toastService.loadingToast();
    this.dashboardService.chosenHeatmapTimePeriod = period;
    this.dashboardService.getData();
  }

  chooseScansByHourTimePeriod(period: TimePeriod) {
    this.toastService.loadingToast();
    this.dashboardService.chosenScansByHourTimePeriod = period;
    this.dashboardService.getData();
  }

  clearChosenStockCheck() {
    this.dashboardService.chosenStockCheckId = null;
    this.dashboardService.chosenStockCheckSite = null;
    this.dashboardService.getData();
  }

  resetBoundary() {
    this.heatmap.fitBounds();
  }

  refresh() {}
}
