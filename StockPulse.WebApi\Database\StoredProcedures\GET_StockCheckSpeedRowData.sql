CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckSpeedRowData]
(
    @UserId INT = NULL,
    @IsActive BIT,
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)  
AS  
BEGIN

  SET NOCOUNT ON;    

  DECLARE @DealerGroupId INT = (SELECT TOP 1 DealerGroupId FROM Users WHERE Id = @UserId);

  -- CTE: ScanStats with TimeToResolveUnknownsHours
  ;WITH ScanStats AS (
    SELECT 
        sca.StockCheckId,
        COUNT(*) AS ScanCount,
        MIN(sca.ScanDateTime) AS FirstScan,
        MAX(sca.ScanDateTime) AS LastScan,
        MAX(ur.ResolutionDateTime) AS LastUnknownResolved,
        AVG(CASE 
              WHEN sca.UnknownResolutionId IS NOT NULL AND ur.ResolutionDateTime IS NOT NULL 
              THEN DATEDIFF(SECOND, sca.ScanDateTime, ur.ResolutionDateTime) * 1.0 
            END) / 3600.0 AS TimeToResolveUnknownsHours
    FROM Scans sca
    INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId AND (@IsActive IS NULL OR SC.IsActive = @IsActive) AND sc.IsRegional = 0 AND sc.IsTotal = 0 AND (@FromDate IS NULL OR sc.Date >= @FromDate) AND (@ToDate IS NULL OR sc.Date <= @ToDate)
    INNER JOIN Sites si ON si.Id = sc.SiteId
    INNER JOIN Divisions d ON d.Id = si.DivisionId AND d.DealerGroupId = @DealerGroupId
    LEFT JOIN UnknownResolutions ur ON ur.Id = sca.UnknownResolutionId
    GROUP BY sca.StockCheckId
  ),

  -- CTE: MinLoadDates for each StockCheckId (to avoid nested aggregates)
  MinLoadDates AS (
    SELECT 
        si.StockCheckId,
        MIN(fi.LoadDate) AS StockLoaded
    FROM StockItems si
    LEFT JOIN import.FileImports fi ON fi.Id = si.FileImportId
    GROUP BY si.StockCheckId
  )

  SELECT
    sc.Id AS StockCheckId,
    s.Description AS Site,
    d.Description AS Division,
    sc.Date AS StockCheckDate,
    st.Description AS Status,
    cu.Name AS CompletedBy,
    au.Name AS ApprovedBy,
    mld.StockLoaded,
    ISNULL(ss.ScanCount, 0) AS Scans,

    -- AVG time to resolve missings, with LoadDate now in a separate join
    AVG(CASE 
          WHEN si.MissingResolutionId IS NOT NULL AND mr.ResolutionDate IS NOT NULL 
          THEN DATEDIFF(SECOND, mld.StockLoaded, mr.ResolutionDate) * 1.0 
        END) / 3600.0 AS TimeToResolveMissingsHours,

    ss.FirstScan,
    ss.LastScan,
    sc.ReconciliationCompletedDate AS ReconciliationCompleted,
    sc.ReconciliationApprovedDate AS ReconciliationApproved,
    MAX(mr.ResolutionDate) AS LastMissingResolved,
    ss.TimeToResolveUnknownsHours

  FROM StockChecks sc
  INNER JOIN Sites s ON s.Id = sc.SiteId
  INNER JOIN Statuses st ON st.Id = sc.StatusId
  INNER JOIN Divisions d ON d.Id = s.DivisionId
  LEFT JOIN Users cu ON cu.Id = sc.ApprovedByAccountantId
  LEFT JOIN Users au ON au.Id = sc.ApprovedById
  LEFT JOIN Users u ON u.Id = @UserId
  LEFT JOIN StockItems si ON si.StockCheckId = sc.Id
  LEFT JOIN MissingResolutions mr ON mr.Id = si.MissingResolutionId
  LEFT JOIN ScanStats ss ON ss.StockCheckId = sc.Id
  LEFT JOIN MinLoadDates mld ON mld.StockCheckId = sc.Id

  WHERE
    (@IsActive IS NULL OR SC.IsActive = @IsActive) AND
    d.DealerGroupId = @DealerGroupId AND
    sc.IsRegional = 0 AND
    sc.IsTotal = 0 AND
    (@FromDate IS NULL OR sc.Date >= @FromDate) AND
    (@ToDate IS NULL OR sc.Date <= @ToDate)

  GROUP BY
    sc.Id, 
    s.Description, 
    d.Description,
    sc.Date,
    st.Description, 
    cu.Name,
    au.Name,
    sc.ReconciliationCompletedDate,
    sc.ReconciliationApprovedDate,
    ss.ScanCount,
    ss.FirstScan,
    ss.LastScan,
    ss.LastUnknownResolved,
    ss.TimeToResolveUnknownsHours,
    mld.StockLoaded

  ORDER BY
    sc.Date DESC, 
    s.Description;

END
GO
