import { <PERSON>mpo<PERSON>, On<PERSON>nit, <PERSON><PERSON>hild, <PERSON>ement<PERSON>ef, HostListener } from '@angular/core';
import { environment } from '../environments/environment';
import { ConfirmModalComponent } from './components/confirmModal/confirmModal.component';
import { NewUserModalComponent } from './pages/userSetup/addUserModal.component';
import { AppStartService } from './services/appStart.service';
import { ConstantsService } from './services/constants.service';
import { VehicleModalComponent } from './components/vehicleModal/vehicleModal.component';
import { AppConfig } from './services/appConfig.service';
import { AuthenticationService } from './services/authentication.service';
import { ToastService } from './services/newToast.service';
import { Router } from '@angular/router';
import { SelectionsService } from './services/selections.service';
import { DealerGroupSelectionModalComponent } from './components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { ScanLocationsModalComponent } from './components/scanLocationsModal/scanLocationsModal.component';
import { MsalService } from '@azure/msal-angular';
import { AuthenticationResult } from '@azure/msal-browser';
import { PreferenceKey } from './model/UserPreference';
import { UserPreferenceService } from './services/userPreference.service';
import { RoleDefinitionsModalComponent } from './components/roleDefinitionsModal/roleDefinitionsModal.component';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: false
})
export class AppComponent implements OnInit {
  appVersion: string;

  @HostListener('window:resize', ['$event'])
  onresize(event) {
    this.constants.smallScreenSize =  event.target.innerWidth < 1680 ? true : false;
  }

  constructor(
    public constants: ConstantsService,
    public appStart: AppStartService,
    private appconfig: AppConfig,
    private authenticationService: AuthenticationService,
    public toastService: ToastService,
    private router: Router,
    public selections: SelectionsService,
    private msalService: MsalService,
    private userPreferenceService: UserPreferenceService
  ) { }

  @ViewChild('addUserModal', { static: true }) addUserModal: NewUserModalComponent;
  @ViewChild('alertModal', { static: true }) alertModal: ElementRef;
  @ViewChild('confirmModal', { static: true }) confirmModal: ConfirmModalComponent;
  @ViewChild('dealerGroupSelectionModal', { static: true }) dealerGroupSelectionModal: DealerGroupSelectionModalComponent;
  @ViewChild('vehicleModal', { static: true }) vehicleModal: VehicleModalComponent;
  @ViewChild('scanLocationsModal', { static: true }) scanLocationsModal: ScanLocationsModalComponent;
  @ViewChild('roleDefintionsModal', { static: true }) roleDefintionsModal: RoleDefinitionsModalComponent;

  ngOnInit() {
    this.initParams()
  }

  initParams() {
    //this.toastService.loadingToast('Starting up...', true);
    this.constants.smallScreenSize =  window.innerWidth < 1680 ? true : false;
    

    //waits for the sso redirect to complete.       
    this.msalService.handleRedirectObservable().subscribe({ 
      next: (result: AuthenticationResult) => {
        if (!this.msalService.instance.getActiveAccount() && this.msalService.instance.getAllAccounts().length > 0) {
          console.log('setting active account', result.account);
            this.msalService.instance.setActiveAccount(result.account);
        }
      },
      error: (error) => console.log(error)
    });

    this.constants.addUserModal = this.addUserModal;
    this.constants.confirmModal = this.confirmModal;
    this.constants.vehicleModal = this.vehicleModal;
    this.constants.dealerGroupSelectionModal = this.dealerGroupSelectionModal;
    this.constants.scanLocationsModal = this.scanLocationsModal;
    this.constants.roleDefinitionsModal = this.roleDefintionsModal;

    this.constants.alertModal = {
      elementRef: this.alertModal,
      title: '',
      message: '',
      showOkInSuccessColour:false
    }

    if (window.location.hostname === 'localhost') {
      this.constants.isDevelopmentEnvironment = true;
    }

    this.appVersion = environment.version;
    this.constants.baseURL = this.appconfig.apiUrl;
    this.constants.baseURLUS = this.appconfig.apiUrlUS;
    this.constants.baseURLUK = this.appconfig.apiUrlUK;

    const haveOldStyleTokens: boolean = this.appStart.getTokensOutOfStorageAndConfirmIfDone();

    if (haveOldStyleTokens){
      this.constants.userLoggedInThroughAzureAD = false;
    }

    if (haveOldStyleTokens || this.constants.userLoggedInThroughAzureAD) {
      setTimeout(() => {
        this.appStart.initiateAppDataLoad();
      }, 500);
    } else {

      //this.authenticationService.logout();
     // this.router.navigateByUrl('/login');
    }
  }

  background(){
    if (this.constants.lightTheme) {
      return this.constants.BackgroundImageURL ? `linear-gradient(rgba(250, 250, 250, 0.7), rgba(250, 250, 250, 0.5)), url(${this.constants.BackgroundImageURL})` : '';
    }

    return this.constants.BackgroundImageURL ? `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url(${this.constants.BackgroundImageURL})` : '';
  }

  get appWrapperClassProvider() {
    if (this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed)) {
      return 'fixSideMenu';
    };
  }
}
