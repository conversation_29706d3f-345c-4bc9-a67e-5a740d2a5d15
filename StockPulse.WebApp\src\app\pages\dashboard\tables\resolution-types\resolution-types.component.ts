import { Component, HostListener } from "@angular/core";
import {
  ColDef,
  ColGroupDef,
  <PERSON>umn,
  ColumnApi,
  Grid<PERSON>pi,
  GridO<PERSON>s,
  GridReadyEvent,
} from "ag-grid-community";
import { Subscription } from "rxjs";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { CphPipe } from "src/app/cph.pipe";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { ToastService } from "src/app/services/newToast.service";
import { DashboardService } from "../../dashboard.service";
import { ResolutionTypesRowFlat } from "src/app/model/ResolutionTypeItem";
import { LogoService } from "src/app/services/logo.service";
import { NavigationStart, Router } from "@angular/router";

@Component({
  selector: "app-resolution-types",
  templateUrl: "./resolution-types.component.html",
  styleUrls: ["./resolution-types.component.scss"],
  standalone: false,
})
export class ResolutionTypesComponent {
  @HostListener("window:resize", [])
  private onresize(event) {
    if (this.gridApi) {
      this.adjustColumnSizing();
    }
  }

  public gridOptions: GridOptions;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;
  private sidenavToggledSubscription: Subscription;
  private routerSubscription: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    public dashboardService: DashboardService,
    public toastService: ToastService,
    public excelExportService: ExcelExportService,

    private logoService: LogoService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    this.sidenavToggledSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  ngOnInit(): void {
    this.initialiseGrid();

    this.sidenavToggledSubscription =
      this.constantsService.sidenavToggledEmitter.subscribe(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });

    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dashboardService.resolutionTypesColumnState =
          this.gridColumnApi.getColumnState();
        this.dashboardService.resolutionTypesfilterModel =
          this.gridApi.getFilterModel();
      }
    });
  }

  initialiseGrid(): void {
    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      headerHeight: 50,
      floatingFiltersHeight: 25,
      rowHeight: 25,
      rowData: this.dashboardService.resolutionTypesRowData,
      context: { thisComponent: this },
      defaultColDef: {
        sortable: true,
        resizable: true,
        floatingFilter: true,
        autoHeight: true,
        autoHeaderHeight: true,
        wrapHeaderText: true,
      },
      columnTypes: {
        label: { filter: "agTextColumnFilter" },
        number: {
          filter: "agTextColumnFilter",
          cellClass: "agAlignRight",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "number", 0)
              : "-",
        },
        date: {
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "dateMed", 0)
              : null,
        },
      },
      columnDefs: this.buildColDefs(
        this.dashboardService.resolutionTypesRowData
      ),
      onGridReady: (event) => this.onGridReady(event),
      onCellDoubleClicked: (event) => {
        let field: string = event.colDef.field;

        if (
          field.includes("Missing") ||
          field.includes("Unknown") ||
          field === "inStock" ||
          field === "inStockAndScanned" ||
          field === "scans"
        ) {
          this.dashboardService.loadStockCheckAndGoToReconcile(
            event.data.stockCheckId,
            event.colDef.field
          );
        }
      },
      onColumnGroupOpened: (event) => {
        this.gridApi.sizeColumnsToFit();
        this.dashboardService.resolutionTypesColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onColumnMoved: (event) => {
        this.dashboardService.resolutionTypesColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onFilterChanged: (event) => {
        this.dashboardService.resolutionTypesfilterModel =
          this.gridApi.getFilterModel();
      },
      onSortChanged: (event) => {
        this.dashboardService.resolutionTypesColumnState =
          this.gridColumnApi?.getColumnState();
      },
    };
  }

  private buildColDefs(
    rowData: ResolutionTypesRowFlat[]
  ): (ColDef<any> | ColGroupDef<any>)[] {
    const staticCols = this.buildStaticCols();

    const exampleRow = rowData[0];
    const missingCols = Object.keys(exampleRow).filter((k) =>
      k.startsWith("Missing_")
    );
    const unknownCols = Object.keys(exampleRow).filter((k) =>
      k.startsWith("Unknown_")
    );
    const missingAutoCols = Object.keys(exampleRow).filter((k) =>
      k.startsWith("MissingAuto_")
    );
    const unknownAutoCols = Object.keys(exampleRow).filter((k) =>
      k.startsWith("UnknownAuto_")
    );

    return [
      {
        headerName: "",
        children: staticCols,
      },
      {
        headerName: "Missing - Auto Reconciled",
        children: missingAutoCols.map((f) => ({
          headerName: f.replace("MissingAuto_", ""),
          type: "number",
          width: 70,
          field: f,
          cellClass: "hover-bold agAlignRight",
        })),
      },
      {
        headerName: "Missing - Manual Resolutions",
        children: missingCols.map((f) => ({
          headerName: f.replace("Missing_", ""),
          type: "number",
          width: 70,
          field: f,
          cellClass: "hover-bold agAlignRight",
        })),
      },
      {
        headerName: "",
        children: [
          {
            headerName: "In Stock and Scanned",
            field: "inStockAndScanned",
            type: "number",
            width: 70,
            cellClass: "hover-bold agAlignRight boldAndShaded",
            headerClass: "boldAndShaded",
          },
        ],
      },
      {
        headerName: "Unknown - Auto Reconciled",
        children: unknownAutoCols.map((f) => ({
          headerName: f.replace("UnknownAuto_", ""),
          type: "number",
          width: 70,
          field: f,
          cellClass: "hover-bold agAlignRight",
        })),
      },
      {
        headerName: "Unknown - Manual Resolutions",
        children: unknownCols.map((f) => ({
          headerName: f.replace("Unknown_", ""),
          type: "number",
          width: 70,
          field: f,
          cellClass: "hover-bold agAlignRight",
        })),
      },
      {
        headerName: "",
        children: [
          {
            headerName: "Scans",
            field: "scans",
            type: "number",
            width: 70,
            cellClass: "hover-bold agAlignRight boldAndShaded",
            headerClass: "boldAndShaded",
          },
        ],
      },
    ];
  }

  private buildStaticCols(): ColDef[] {
    return [
      {
        headerName: "Id",
        field: "stockCheckId",
        type: "label",
        width: 60,
        pinned: "left",
      },
      {
        headerName: "Site",
        field: "site",
        type: "label",
        width: 150,
        pinned: "left",
      },
      {
        headerName: "Division",
        field: "division",
        type: "label",
        width: 100,
        pinned: "left",
      },
      {
        headerName: "Stock Check Date",
        field: "stockCheckDate",
        type: "date",
        width: 100,
        pinned: "left",
      },
      {
        headerName: "Status",
        valueGetter: (params) => {
          return this.constantsService.abbreviateStockCheckStatus(
            params.data.status
          );
        },
        type: "label",
        width: 120,
        pinned: "left",
      },
      {
        headerName: "Completed By",
        field: "completedBy",
        type: "label",
        width: 100,
        pinned: "left",
      },
      {
        headerName: "In Stock",
        field: "inStock",
        type: "number",
        width: 75,
        cellClass: "hover-bold agAlignRight boldAndShaded",
        headerClass: "boldAndShaded",
      },
    ];
  }

  onGridReady(event: GridReadyEvent): void {
    this.gridApi = event.api;
    this.gridColumnApi = event.columnApi;

    if (this.dashboardService.resolutionTypesColumnState) {
      this.gridColumnApi.applyColumnState({
        state: this.dashboardService.resolutionTypesColumnState,
        applyOrder: true,
      });
    }
    if (this.dashboardService.resolutionTypesfilterModel) {
      this.gridApi.setFilterModel(
        this.dashboardService.resolutionTypesfilterModel
      );
    }

    this.adjustColumnSizing();
  }

  download(): void {
    this.toastService.loadingToast("Generating Excel file...");

    const filteredRows = [];
    const columns: Column[] = this.gridColumnApi.getAllDisplayedColumns();

    this.gridApi.forEachNodeAfterFilter((n) => {
      const columnsForExcel = {};

      columns.forEach((column) => {
        const columnId: string = column.getColId();
        columnsForExcel[columnId] = n.data[columnId];
      });

      columnsForExcel["stockCheckDate"] = new Date(n.data.stockCheckDate);

      filteredRows.push(columnsForExcel);
    });

    const colTypes: string[] = [];

    this.gridApi.getColumnDefs().forEach((colDef) => {
      if ((colDef as ColGroupDef).children) {
        (colDef as ColGroupDef).children.forEach((child) => {
          colTypes.push((child as ColDef).type as string);
        });
      } else {
        colTypes.push((colDef as ColDef).type as string);
      }
    });

    let sheet: SheetToExtractOld = {
      tableData: filteredRows,
      tableName: `Resolution Types`,
      columnWidths: this.excelExportService.workoutColWidths(filteredRows),
      colTypes: colTypes,
    };

    this.dashboardService.downloadExcelFile(sheet, 6);
  }

  get excelIconBackgroundImage() {
    return {
      "background-image": `url(${this.logoService.provideExcelLogo()})`,
    };
  }

  adjustColumnSizing(): void {
    if (!this.gridApi || !this.gridColumnApi) return;

    // Total width of all columns
    const allColumns = this.gridColumnApi.getAllDisplayedColumns();
    const totalColumnWidth = allColumns.reduce((sum, col) => {
      return (
        sum +
          this.gridColumnApi
            .getColumnState()
            .find((c) => c.colId === col.getColId())?.width || 0
      );
    }, 0);

    // Width of the grid container
    const gridElement = document.querySelector<HTMLElement>(
      "#resolutionTypesGrid"
    );
    const gridWidth = gridElement?.offsetWidth || 0;

    if (totalColumnWidth < gridWidth) {
      this.gridApi.sizeColumnsToFit();
    }
  }
}
