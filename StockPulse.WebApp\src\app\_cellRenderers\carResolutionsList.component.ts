import { Component } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";


@Component({
    selector: 'carResolutionsList-cell',
    template: `
        
        <div class="spaceBetween column" >

     
        
        <!-- each result -->
        <div class="resolutionsContainer spaceBetween" *ngFor="let each of params.data.allStockCheckResults">
            
            <div class="stockCheck">{{each.stockCheckDescription}}</div>

            <ng-container *ngIf="type == 'missing'">
                <div *ngIf="!each.stockCheckItem.problemResolutionType" class="resolutionDescription">Unresolved</div>
                <div *ngIf="each.stockCheckItem.problemResolutionType" class="resolutionDescription">{{each.stockCheckItem.problemResolutionType.Description}}</div>
                <div class="resolutionNotes">{{each.stockCheckItem.Notes}}</div>
            </ng-container>
            <ng-container *ngIf="type == 'unknown'">
                <div *ngIf="!each.stockCheckItem.Scan.problemResolutionType" class="resolutionDescription">Unresolved</div>
                <div *ngIf="each.stockCheckItem.Scan.problemResolutionType" class="resolutionDescription">{{each.stockCheckItem.Scan.problemResolutionType.Description}}</div>
                <div class="resolutionNotes">{{each.stockCheckItem.Scan.Notes}}</div>
            </ng-container>



        </div>
        
        </div>
        
        `,
    styles: [
        `
        .cellContentBox{    white-space: normal;            line-height: 1.5em;            }
      `
    ],
    standalone: false
})
export class CarResolutionsListComponent implements ICellRendererAngularComp {
    params: any;
    type:string;
    constructor(    ) { }

    agInit(params: any): void {

       
        this.params = params;
        

        this.type = params.colDef.colId

    }
    refresh(): boolean {
        return false;
    }
}


