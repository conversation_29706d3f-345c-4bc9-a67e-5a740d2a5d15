import { ResolutionReportVM } from "./ResolutionReportVM";



export interface ResolutionTypeItem {
  stockCheckId: number;
  site: string;
  division: string;
  stockCheckDate: Date | string;
  status: string;
  completedBy: string;
  inStock: number;
  inStockAndScanned: number;
  scans: number;
  duplicateStockItems: number;
  duplicateScans: number;
  missingResolutions: ResolutionReportVM[];
  unknownResolutions: ResolutionReportVM[];
  missingAutoResolutions: ResolutionReportVM[];
  unknownAutoResolutions: ResolutionReportVM[];
}



export interface ResolutionTypeRowBase {
  stockCheckId: number;
  site: string;
  division: string;
  stockCheckDate: string;
  status: string;
  completedBy: string;
  inStock: number;
}

export interface ResolutionTypesRowFlat extends ResolutionTypeRowBase {
  [key: string]: number | string;
}