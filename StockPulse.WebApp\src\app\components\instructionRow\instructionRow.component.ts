import { Component, ContentChild, Input, OnInit, TemplateRef } from '@angular/core';
import { IconService } from 'src/app/services/icon.service';




@Component({
    selector: 'instructionRow',
    templateUrl: './instructionRow.component.html',
    styleUrls: ['./instructionRow.component.scss'],
    standalone: false
})
export class InstructionRowComponent  {
 @Input() message:string ;
 @Input() isDanger:boolean;
 @Input() noMargin: boolean;

 @ContentChild(TemplateRef) public inputElement: TemplateRef<any>;

  constructor(
    public icon: IconService
  ) {
  }


}
