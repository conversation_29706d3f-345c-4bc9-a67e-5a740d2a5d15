//-----------------------------------
//angular stuff
import { BrowserModule } from '@angular/platform-browser';
import { APP_INITIALIZER, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpClient, HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi, withJsonpSupport } from '@angular/common/http';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { AppComponent } from './app.component';
import { CurrencyPipe, DecimalPipe, DatePipe, CommonModule } from '@angular/common';
import { CphPipe } from './cph.pipe'
import { JwtInterceptor } from './jwt.interceptor';
import { AppRoutingModule } from './app-routing.module';
import { ErrorInterceptor } from './error.interceptor';
import { HotToastModule } from '@ngneat/hot-toast';



//-----------------------------------
//3rd party
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { AgGridModule } from 'ag-grid-angular';
import { GoogleMapsModule } from '@angular/google-maps'; //maps
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';





//-----------------------------------
//Pages
import { GlobalSearchComponent } from './pages/globalSearch/globalSearch.component';
import { HomeComponent } from './pages/home/<USER>';
import { ReconcileComponent } from './pages/reconcile/reconcile.component';
import { DmsStockTableComponent } from './pages/loadItems/dmsStockTable/dmsStockTable.component';
import { ReconcilingItemsComponent } from './pages/loadItems/loadItems.component';
import { ReconcilingItemsTableComponent } from './pages/loadItems/reconcilingItemsTable/reconcilingItemsTable.component';
import { RepeatMissingsTableComponent } from './pages/repeatOffenders/repeatMissingsTable.component';
import { RepeatOffendersComponent } from './pages/repeatOffenders/repeatOffenders.component';
import { RepeatUnknownsTableComponent } from './pages/repeatOffenders/repeatUnknownsTable.component';
import { ImportTbItemsComponent } from './pages/signoff/importTbItems.component';
import { ImportTbTableComponent } from './pages/signoff/importTbTable.component';
import { SignoffComponent } from './pages/signoff/signoff.component';
import { StockChecksComponent } from './pages/stockChecks/stockChecks.component';
import { StockChecksTableComponent } from './pages/stockChecks/table/stockChecksTable.component';
import { NewUserModalComponent } from './pages/userSetup/addUserModal.component';
import { CancelButtonUserSetupComponent } from './pages/userSetup/cancelButton';
import { DeleteButtonUserComponent } from './pages/userSetup/deleteButton';
import { DeleteSiteNameLookupComponent } from './components/siteNameLookupsModal/deleteSiteNameLookup';
import { RolePickerCellRendererComponent } from './pages/userSetup/rolePicker.component';
import { SaveButtonUserComponent } from './pages/userSetup/saveButton';
import { SitePickerCellRendererComponent } from './pages/userSetup/sitePickerCell.component';
import { SitesPickerCellRendererComponent } from './pages/userSetup/sitesPickerCell.component';
import { UserSetupComponent } from './pages/userSetup/userSetup.component';
import { VehiclesWithWrongRegComponent } from './pages/vehiclesWithWrongReg/vehiclesWithWrongReg.component';



//-----------------------------------
//Components
import { SitePickerComponent } from './components/sitePicker/sitePicker.component';
import { WrongRegItemComponent } from './pages/vehiclesWithWrongReg/wrongRegItem/wrongRegItem.component';
import { ConfirmModalComponent } from './components/confirmModal/confirmModal.component';
import { StatusComponent } from './components/status/status.component';
import { VehicleModalComponent } from './components/vehicleModal/vehicleModal.component';


//-----------------------------------
//Cell Renderers
import { CustomHeaderComponent } from './_cellRenderers/customHeader.component';
import { IndexComponent } from './_cellRenderers/index.component';
import { RegPlateComponent } from './_cellRenderers/regPlate.component';
import { ChassisComponent } from './_cellRenderers/chassis.component';
import { ConfidenceComponent } from './_cellRenderers/confidence.component';
import { ImageComponent } from './_cellRenderers/image.component';
import { DeleteButtonComponent } from './_cellRenderers/deleteButton.component';
import { ScanNotesComponent } from './_cellRenderers/scanNotes.component';
import { CarResolutionComponent } from './_cellRenderers/carResolution.component';
import { CarNotesComponent } from './_cellRenderers/carNotes.component';
import { ImportHeaderComponent } from './_cellRenderers/importHeader.component';
import { CarResolutionsListComponent } from './_cellRenderers/carResolutionsList.component';
import { ImportItemsComponent } from './pages/loadItems/import/import.component';
import { ImportTableComponent } from './pages/loadItems/import/importTable/importTable.component';
import { LoginComponent } from './pages/login/login.component';
import { ForgotpasswordComponent } from './pages/forgotpassword/forgotpassword.component';
import { RouterModule } from '@angular/router';
import { ResetPasswordComponent } from './pages/resetPassword/resetPassword.component';
import { AppConfig } from './services/appConfig.service';
import { ErrorHandlerService } from './services/errorHandler.service';
import { ApplicationInsightLoggingService } from './services/applicationInsightLogging.service';
import { BarComponent } from './_cellRenderers/bar.component';
import { NavbarComponent } from './components/navbar/navbar.component';
import { SidenavComponent } from './components/sidenav/sidenav.component';
import { DragAndDropDirective } from './components/vehicleModal/drag-and-drop.directive';
// import { ImageHoverTooltipComponent } from './_cellRenderers/imageHoverTooltip.component';
//import { ImportInterceptor } from './import.interceptor';
import { PhotoMapComponent } from './pages/photoMap/photoMap.component';



import { IPublicClientApplication, PublicClientApplication, InteractionType } from '@azure/msal-browser';
import {
    MsalGuard, MsalBroadcastService, MsalInterceptorConfiguration, MsalModule, MsalService,
    MSAL_GUARD_CONFIG, MSAL_INSTANCE, MSAL_INTERCEPTOR_CONFIG, MsalGuardConfiguration, MsalRedirectComponent, ProtectedResourceScopes
} from '@azure/msal-angular';

import { msalConfig, loginRequest, protectedResources } from './auth-config';
import { SliderSwitchComponent } from './components/sliderSwitch/sliderSwitch.component';
import { InstructionRowComponent } from './components/instructionRow/instructionRow.component';
import { ReportsExportComponent } from './pages/reconcile/reportsExport/reportsExport.component';
import { VehicleModalBodyComponent } from './components/vehicleModal/vehicleModalBody/vehicleModalBody.component';
import { VehicleModalBodyNewComponent } from './components/vehicleModal/vehicleModalBodyNew/vehicleModalBodyNew.component';
import { InstructionRowPopoverStyleComponent } from './components/instructionRowPopoverStyle/instructionRowPopoverStyle.component';
import { TrialBalanceTableComponent } from './pages/loadItems/trialBalanceTable/trialBalanceTable.component';
import { BackupImageComponent } from './_cellRenderers/backup-image/backup-image.component';
import { ExplanationComponent } from './components/vehicleModal/vehicleModalBodyNew/explanation/explanation.component';
import { DealerGroupSelectionModalComponent } from './components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { ReviewImportMapsModal } from './pages/loadItems/reviewImportMapsModal/reviewImportMapsModal.component';
import { DeleteImportMapButtonComponent } from './pages/loadItems/reviewImportMapsModal/deleteButton';
import { CheckboxIconComponent } from './_cellRenderers/checkboxIcon';
import { ScanLocationsModalComponent } from './components/scanLocationsModal/scanLocationsModal.component';
import { LabelPrinterComponent } from './pages/labelPrinter/labelPrinter.component';
import { LabelPrinterTableComponent } from './pages/labelPrinter/table/table.component';
import { LabelPrinterModalComponent } from './pages/labelPrinter/modal/modal.component';
import { BulkResolveComponent } from './components/bulkResolve/bulkResolve.component';
import { CheckboxComponent } from './_cellRenderers/checkBox';
import { ReconcilingReportBackupComponent } from './components/reconcilingReportBackup/reconcilingReportBackup.component';
import { StatusAndBarChartComponent } from './components/statusAndBarChart/statusAndBarChart.component';
import { SingleSitePickerWithSearchComponent } from './components/singleSitePickerWithSearch/singleSitePickerWithSearch.component';
import { SiteNameLookupsModalComponent } from './components/siteNameLookupsModal/siteNameLookupsModal.component';
import { SitePickerSimple } from './_cellRenderers/SitePickerSimple.component';
import { DeleteButtonTableMaintenanceComponent } from './components/tableMaintenance/deleteButtonTableMaintenance.component';
import { SaveButtonTableMaintenanceComponent } from './components/tableMaintenance/saveButtonTableMaintenance.component';
import { TableMaintenanceComponent } from './components/tableMaintenance/tableMaintenance.component';
import { TableMaintenanceTableComponent } from './components/tableMaintenance/tableMaintenanceTable.component';
import { RestoreButtonUserComponent } from './pages/userSetup/restoreButton';
import { RoleDefinitionsModalComponent } from './components/roleDefinitionsModal/roleDefinitionsModal.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { ScansByHourComponent } from './pages/dashboard/components/scansByHour/scansByHour.component';
import { StockCheckStatusComponent } from './pages/dashboard/components/stockCheckStatus/stockCheckStatus.component';
import { StockCheckSummaryComponent } from './pages/dashboard/components/stockCheckSummary/stockCheckSummary.component';
import { HeatmapComponent } from './pages/dashboard/components/heatmap/heatmap.component';
import { ResolutionTypesComponent } from './pages/dashboard/tables/resolution-types/resolution-types.component';
import { DataLoadedComponent } from './pages/dashboard/tables/data-loaded/data-loaded.component';
import { StockCheckQualityComponent } from './pages/dashboard/tables/stock-check-quality/stock-check-quality.component';
import { StockCheckSpeedComponent } from './pages/dashboard/tables/stock-check-speed/stock-check-speed.component';
import { LeafletModule } from '@bluehalo/ngx-leaflet';

/**
 * Here we pass the configuration parameters to create an MSAL instance.
 * For more info, visit: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-angular/docs/v2-docs/configuration.md
 */
export function MSALInstanceFactory(): IPublicClientApplication {
  return new PublicClientApplication(msalConfig);
}


/**
* MSAL Angular will automatically retrieve tokens for resources
* added to protectedResourceMap. For more info, visit:
* https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-angular/docs/v2-docs/initialization.md#get-tokens-for-web-api-calls
*/
export function MSALInterceptorConfigFactory(appConfig: AppConfig): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string | ProtectedResourceScopes> | null>();

  protectedResources.api.endpoint == appConfig.apiUrl;

  protectedResourceMap.set(protectedResources.api.endpoint, [
      {
          httpMethod: 'GET',
          scopes: [...protectedResources.api.scopes]
      },
      {
          httpMethod: 'POST',
          scopes: [...protectedResources.api.scopes]
      },
      {
          httpMethod: 'PUT',
          scopes: [...protectedResources.api.scopes]
      },
      {
          httpMethod: 'DELETE',
          scopes: [...protectedResources.api.scopes]
      }
  ]);


  return {
      interactionType: InteractionType.Redirect,
      protectedResourceMap,
  };
}

/**
* Set your default interaction type for MSALGuard here. If you have any
* additional scopes you want the user to consent upon login, add them here as well.
*/
export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return {
      interactionType: InteractionType.Redirect,
      authRequest: loginRequest
  };
}




@NgModule({ declarations: [
        AppComponent,
        CphPipe,
        HomeComponent,
        SignoffComponent,
        ReconcilingItemsComponent,
        ReconcileComponent,
        VehiclesWithWrongRegComponent,
        RepeatOffendersComponent,
        VehicleModalComponent,
        VehicleModalBodyComponent,
        IndexComponent,
        RegPlateComponent,
        ChassisComponent,
        ConfidenceComponent,
        ImageComponent,
        DeleteButtonComponent,
        CheckboxIconComponent,
        ScanNotesComponent,
        CarResolutionComponent,
        CarNotesComponent,
        GlobalSearchComponent,
        ReconcilingItemsTableComponent, ImportItemsComponent, ImportTableComponent,
        DmsStockTableComponent, ImportTbItemsComponent, ImportTbTableComponent, TrialBalanceTableComponent,
        StatusComponent,
        RepeatMissingsTableComponent,
        RepeatUnknownsTableComponent,
        CarResolutionsListComponent,
        CustomHeaderComponent,
        ImportHeaderComponent,
        WrongRegItemComponent,
        StockChecksComponent,
        StockChecksTableComponent,
        SitePickerComponent,
        NewUserModalComponent,
        ConfirmModalComponent,
        DealerGroupSelectionModalComponent,
        UserSetupComponent,
        InstructionRowComponent,
        InstructionRowPopoverStyleComponent,
        DeleteButtonUserComponent,
        RestoreButtonUserComponent,
        DeleteSiteNameLookupComponent,
        SitesPickerCellRendererComponent,
        SitePickerCellRendererComponent,
        SliderSwitchComponent,
        CancelButtonUserSetupComponent,
        SaveButtonUserComponent,
        RolePickerCellRendererComponent,
        LoginComponent,
        ForgotpasswordComponent,
        ResetPasswordComponent,
        BarComponent,
        NavbarComponent,
        SidenavComponent,
        DragAndDropDirective,
        PhotoMapComponent,
        ReportsExportComponent,
        BackupImageComponent,
        VehicleModalBodyNewComponent,
        ExplanationComponent,
        ReviewImportMapsModal,
        DeleteImportMapButtonComponent,
        ScanLocationsModalComponent,
        LabelPrinterComponent,
        LabelPrinterTableComponent,
        LabelPrinterModalComponent,
        BulkResolveComponent,
        CheckboxComponent,
        ReconcilingReportBackupComponent,
        StatusAndBarChartComponent,
        SingleSitePickerWithSearchComponent,
        SiteNameLookupsModalComponent,
        SitePickerSimple,
        TableMaintenanceComponent,
        TableMaintenanceTableComponent,
        SaveButtonTableMaintenanceComponent,
        DeleteButtonTableMaintenanceComponent,
        RoleDefinitionsModalComponent,
        DashboardComponent,
        ScansByHourComponent,
        StockCheckStatusComponent,
        StockCheckSummaryComponent,
        HeatmapComponent,
        ResolutionTypesComponent,
        DataLoadedComponent,
        StockCheckQualityComponent,
        StockCheckSpeedComponent
    ],
    bootstrap: [AppComponent, MsalRedirectComponent],
    exports: [
        CphPipe,
    ], imports: [CommonModule,
        RouterModule,
        NgbModule,
        BrowserModule,
        AppRoutingModule,
        ReactiveFormsModule,
        FormsModule,
        ScrollingModule,
        AgGridModule,
        FontAwesomeModule,
        GoogleMapsModule,
        MsalModule,
        HotToastModule.forRoot(),
        LeafletModule
        ], providers: [
        //   {
        //     provide: HTTP_INTERCEPTORS,
        //     useClass: MsalInterceptor,
        //     multi: true
        // },
        {
            provide: MSAL_INSTANCE,
            useFactory: MSALInstanceFactory
        },
        {
            provide: MSAL_GUARD_CONFIG,
            useFactory: MSALGuardConfigFactory
        },
        {
            provide: MSAL_INTERCEPTOR_CONFIG,
            useFactory: MSALInterceptorConfigFactory
        },
        MsalService,
        MsalGuard,
        MsalBroadcastService,
        { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
        //{ provide: HTTP_INTERCEPTORS, useClass: ImportInterceptor, multi: true },
        CurrencyPipe,
        DecimalPipe,
        DatePipe,
        CphPipe,
        VehicleModalComponent,
        DealerGroupSelectionModalComponent,
        AppConfig,
        {
            provide: APP_INITIALIZER,
            useFactory: AppConfigurationFactory,
            deps: [AppConfig, HttpClient], multi: true
        },
        ApplicationInsightLoggingService,
        { provide: ErrorHandler, useClass: ErrorHandlerService },
        provideHttpClient(withInterceptorsFromDi(), withJsonpSupport())
    ] })
export class AppModule { }

export function  AppConfigurationFactory(
  appConfig: AppConfig) {
    return () => appConfig.ensureInit();
  }
