import { Component } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";


@Component({
    selector: 'carResolution-cell',
    template: `
        <div class="cellContentBox">{{show}}</div>`,
    styles: [
        `.cellContentBox{    white-space: normal;            line-height: 1.5em;            }
      `
    ],
    standalone: false
})
export class CarResolutionComponent implements ICellRendererAngularComp {
    show: string = '';
    constructor(    ) { }

    agInit(params: any): void {

        if (params.value ) {
            this.show = params.value;
            
        } else {
        this.show = '';    
        
        }
    }
    refresh(): boolean {
        return false;
    }
}


