CREATE OR ALTER PROCEDURE [dbo].[GET_ScansByHourItems]
(
    @UserId INT ,
    @StartDateTime DateTime,
    @EndDateTime DateTime,
    @StockCheckId INT = NULL
)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @DealerGroupId INT;
    SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;


    SELECT
        DATEADD(HOUR, DATEDIFF(HOUR, 0, s.ScanDateTime), 0) AS Hour,
        COUNT(*) AS Count
    FROM Scans s
    INNER JOIN StockChecks sc ON sc.Id = s.StockCheckId
    INNER JOIN Sites si ON si.Id = sc.SiteId
    INNER JOIN Divisions d ON d.Id = si.DivisionId
    WHERE
        d.DealerGroupId = @DealerGroupId AND
        sc.IsActive = 1 AND
        s.ScanDateTime >= @StartDateTime AND
        s.ScanDateTime <= @EndDateTime AND
        (@StockCheckId IS NULL OR s.StockCheckId = @StockCheckId)
    GROUP BY
        DATEADD(HOUR, DATEDIFF(HOUR, 0, s.ScanDateTime), 0)
END;
