﻿using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class DataLoadedItem
    {
        public int StockCheckId { get; set; }
        public string Site { get; set; }
        public string Division { get; set; }
        public DateTime StockCheckDate { get; set; }
        public string Status { get; set; }
        public string CompletedBy { get; set; }
        public int InStock { get; set; }
        public int Scans { get; set; }
        public List<DataLoadedReportVM> MissingReports { get; set; } = new List<DataLoadedReportVM>();
        public List<DataLoadedReportVM> UnknownReports { get; set; } = new List<DataLoadedReportVM>();
    }
}
