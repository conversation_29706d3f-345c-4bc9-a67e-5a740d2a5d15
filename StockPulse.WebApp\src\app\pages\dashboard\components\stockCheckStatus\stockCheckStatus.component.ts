import {
  Component,
  OnInit,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  OnDestroy,
} from "@angular/core";
import { Subscription } from "rxjs";
import { Chart } from "chart.js/auto";
import { StockCheckSummarySimple } from "src/app/model/StockCheckSummarySimple";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { ConstantsService } from "src/app/services/constants.service";
import { DashboardService } from "../../dashboard.service";

@Component({
  selector: "stockCheckStatus",
  templateUrl: "./stockCheckStatus.component.html",
  styleUrls: ["./stockCheckStatus.component.scss"],
})
export class StockCheckStatusComponent implements OnInit, OnDestroy {
  public chart: any;

  private subscription: Subscription;

  constructor(
    private constantsService: ConstantsService,
    public dashboardService: DashboardService
  ) {}

  get data(): StockCheckSummarySimple[] {
    return this.dashboardService.dashboard.stockCheckSummaryItems;
  }

  ngOnInit(): void {
    this.createChart();

    // Subscribe to the newDataEmitter
    this.subscription = this.dashboardService.newDataEmitter.subscribe(() => {
      this.reactToChanges();
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  reactToChanges() {
    if (this.chart) {
      const dataSets = this.initialiseChartData();

      (this.chart.data.labels = dataSets.labels),
        (this.chart.data.datasets = [
          {
            data: dataSets.data,
            backgroundColor: dataSets.backgroundColours,
          },
        ]),
        this.chart.update();
    } else {
      setTimeout(() => {
        this.createChart();
      }, 50);
    }
  }

  // This method is now handled in ngOnInit

  initialiseChartData(): {
    data: number[];
    backgroundColours: string[];
    labels: string[];
  } {
    const labels = [...new Set(this.data.map((x) => x.status))].sort((a, b) => {
      const statusOrder = this.constantsService.Statuses.map(
        (s) => s.description
      );
      return statusOrder.indexOf(a) - statusOrder.indexOf(b);
    });

    const backgroundColours: string[] = [];

    if (labels.includes("Not Started")) {
      backgroundColours.push("#7F7F7F");
    }
    if (labels.includes("Scans In Progress")) {
      backgroundColours.push("#FFBF00");
    }
    if (labels.includes("Scans Completed")) {
      backgroundColours.push("#61CBF4");
    }
    if (labels.includes("Reconciliation Completed")) {
      backgroundColours.push("#47D45A");
    }
    if (labels.includes("Reconciliation Approved")) {
      backgroundColours.push("#00B050");
    }

    return {
      data: this.groupDataByStatus(),
      backgroundColours: backgroundColours,
      labels: labels,
    };
  }

  createChart() {
    const dataSets = this.initialiseChartData();

    this.chart = new Chart("stockCheckStatus", {
      type: "pie",
      data: {
        labels: dataSets.labels,
        datasets: [
          {
            data: dataSets.data,
            backgroundColor: dataSets.backgroundColours,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: false,
        layout: {
          padding: 30,
        },
        plugins: {
          legend: {
            position: "bottom",
            title: {
              display: true,
              text: "",
            },
          },
          datalabels: {
            anchor: "end",
            align: "end",
            formatter: (value, context) => {
              let dataset: number[] = context.chart.data.datasets[0]
                .data as number[];
              let total: number = dataset.reduce((sum, val) => sum + val, 0);
              let percentage: string = ((value / total) * 100).toFixed(0);
              return `${percentage}%`;
            },
          },
          tooltip: {
            callbacks: {
              label: (tooltipItem) => {
                return "";
              },
            },
          },
        },
      },
      plugins: [ChartDataLabels],
    });
  }

  groupDataByStatus() {
    let uniqueStatuses = [
      ...new Set(
        this.data
          .map((x) => x.status)
          .sort((a, b) => {
            const statusOrder = this.constantsService.Statuses.map(
              (s) => s.description
            );
            return statusOrder.indexOf(a) - statusOrder.indexOf(b);
          })
      ),
    ];
    return uniqueStatuses.map(
      (status) => this.data.filter((x) => x.status === status).length
    );
  }
}
