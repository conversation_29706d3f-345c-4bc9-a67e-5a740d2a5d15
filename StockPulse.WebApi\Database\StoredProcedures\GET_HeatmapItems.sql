CREATE OR ALTER PROCEDURE [dbo].[GET_HeatmapItems]  
(
    @UserId INT,
    @heatmapStartDate DateTime,
    @heatmapEndDate DateTime,
    @StockCheckId INT = NULL
)  
AS  
BEGIN

  SET NOCOUNT ON;

  DECLARE @DealerGroupId INT;
  SELECT @DealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId;

  --DECLARE @TimePeriodDateTime datetime = DATEADD(HOUR, -@TimePeriod, GETDATE());
  
  SELECT 
        s.Latitude, 
        s.Longitude
    FROM Scans s
	INNER JOIN StockChecks sc ON sc.Id = s.StockCheckId
	INNER JOIN Sites si ON si.Id = sc.SiteId	
	INNER JOIN Divisions d ON d.Id = si.DivisionId
    WHERE 
        s.ScanDateTime >= @heatmapStartDate
        AND s.ScanDateTime <= @heatmapEndDate
		AND d.DealerGroupId = @DealerGroupId
        AND sc.IsActive = 1
        AND (@StockCheckId IS NULL OR sc.Id = @StockCheckId)
END;
