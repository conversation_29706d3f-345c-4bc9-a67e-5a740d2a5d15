import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { TableMaintenanceService } from "./tableMaintenance.service";
import { IconService } from "src/app/services/icon.service";



// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'saveButtonTableMaintenance-cell',
    template: `
   
        <button class="btn btn-success" (click)="save()">
        <fa-icon [icon]="icon.faSave"> Save </fa-icon>
            
            
            </button>    
            
    

    `,
    styles: [
        `
        button{
            position: absolute;
            width: 70%;
            height: 100%;
            top: 0;
            left: 15%;
            margin: 0;
            display: flex;
            justify-content: center;
            border-radius: 0.25rem;
            align-items: center;
        }
       
      `
    ],
    standalone: false
})
export class SaveButtonTableMaintenanceComponent implements ICellRendererAngularComp {
   
params:any;


    constructor(
        
        //public apiAccess: ApiAccessService,
        public service: TableMaintenanceService,
        public icon: IconService

    ) { }


    agInit(params: any): void {

    this.params = params

    }
    refresh(): boolean {
        return false;
    }


    save(){

        console.log('call api to save the row.', this.params.data);

         this.service.saveRow(this.service.selectedTableName, this.params.data);
        
        // let userAndLogin = this.params.data;

        // //build new object
        // let itemToPersist: UserAndLogin = {
        //     Id: userAndLogin.Id,
        //     Name: userAndLogin.newValues.Name || userAndLogin.Name,
        //     UserName: userAndLogin.UserName,
        //     EngineeringSystems: userAndLogin.newValues.EngineeringSystems !== null ? userAndLogin.newValues.EngineeringSystems : userAndLogin.EngineeringSystems,
        //     Email: userAndLogin.Email,
        //     Projects: userAndLogin.newValues.Projects !== null ? userAndLogin.newValues.Projects : userAndLogin.Projects,
        //     Role: null
        // }

        // this.getdata.patch('api/Users','UserAndLogin',itemToPersist).subscribe((data) => {
        //          //success!

        //     //put new values to existing
        //     userAndLogin.Name = itemToPersist.Name,
        //     userAndLogin.UserName = itemToPersist.UserName,
        //     userAndLogin.Email = itemToPersist.Email,
        //     userAndLogin.EngineeringSystems = itemToPersist.EngineeringSystems,
        //     userAndLogin.Projects = itemToPersist.Projects,
        //     userAndLogin.Role = itemToPersist.Role

        //     userAndLogin.newValues = {
        //         Name: null,
        //         UserName: null,
        //         Email: null,
        //         Projects: null,
        //         EngineeringSystems: null,
        //         Role: null
        //     }
        //     userAndLogin.hasChanged = false;
        //     this.params.api.redrawRows()

        //     this.constants.toastSuccess('Saved User')

        // },(e)=>{
        //     this.constants.toastDanger('Failed to save User')
        // })
        

    }

}


