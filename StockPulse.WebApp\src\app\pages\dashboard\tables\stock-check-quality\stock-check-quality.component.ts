import { Component, HostListener } from "@angular/core";
import {
  ColDef,
  ColGroupDef,
  <PERSON>umn,
  ColumnApi,
  GridApi,
  GridOptions,
  GridReadyEvent,
} from "ag-grid-community";
import { Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { ToastService } from "src/app/services/newToast.service";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { DashboardService } from "../../dashboard.service";
import { LogoService } from "src/app/services/logo.service";
import { NavigationStart, Router } from "@angular/router";

@Component({
  selector: "app-stock-check-quality",
  templateUrl: "./stock-check-quality.component.html",
  styleUrls: ["./stock-check-quality.component.scss"],
})
export class StockCheckQualityComponent {
  @HostListener("window:resize", [])
  private onresize(event) {
    if (this.gridApi) {
      this.adjustColumnSizing();
    }
  }

  public gridOptions: GridOptions;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;
  private sidenavToggledSubscription: Subscription;
  private routerSubscription: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    public toastService: ToastService,
    public excelExportService: ExcelExportService,
    public dashboardService: DashboardService,

    private logoService: LogoService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    this.sidenavToggledSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  ngOnInit(): void {
    this.initialiseGrid();

    this.sidenavToggledSubscription =
      this.constantsService.sidenavToggledEmitter.subscribe(() => {
        if (this.gridApi) {
          this.gridApi.sizeColumnsToFit();
        }
      });

    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dashboardService.stockCheckQualityColumnState =
          this.gridColumnApi.getColumnState();
        this.dashboardService.stockCheckQualityfilterModel =
          this.gridApi.getFilterModel();
      }
    });
  }

  initialiseGrid(): void {
    const numberColWidth = 85;
    const groupedOpenColWidth = 100;

    this.gridOptions = {
      components: { agColumnHeader: CustomHeaderComponent },
      headerHeight: 50,
      floatingFiltersHeight: 25,
      rowHeight: 25,
      context: { thisComponent: this },
      defaultColDef: {
        sortable: true,
        resizable: true,
        floatingFilter: true,
        autoHeight: true,
        autoHeaderHeight: true,
        wrapHeaderText: true,
      },
      columnTypes: {
        label: { filter: "agTextColumnFilter" },
        number: {
          filter: "agTextColumnFilter",
          cellClass: "agAlignRight",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "number", 0)
              : "-",
        },
        date: {
          filter: "agDateColumnFilter",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "dateMed", 0)
              : null,
        },
        percent: {
          filter: "agTextColumnFilter",
          cellClass: "agAlignRight",
          valueFormatter: (params) =>
            params.value
              ? this.cphPipe.transform(params.value, "percent", 0)
              : "-",
        },
      },
      columnDefs: [
        //Detail of stockcheck
        {
          headerName: "Id",
          field: "stockCheckId",
          width: 60,
          type: "label",
          pinned: "left",
        },
        {
          headerName: "Site",
          field: "site",
          width: 150,
          type: "label",
          pinned: "left",
        },
        {
          headerName: "Division",
          field: "division",
          type: "label",
          width: 80,
          pinned: "left",
        },
        {
          headerName: "Stock Check Date",
          field: "stockCheckDate",
          type: "date",
          width: 100,
          pinned: "left",
        },
        {
          headerName: "Status",
          valueGetter: (params) => {
            return this.constantsService.abbreviateStockCheckStatus(
              params.data.status
            );
          },
          width: 120,
          type: "label",
          pinned: "left",
        },
        {
          headerName: "Completed By",
          field: "completedBy",
          width: 130,
          type: "label",
          pinned: "left",
        },
        {
          headerName: "Last Updated",
          field: "lastUpdated",
          width: 100,
          type: "date",
          pinned: "left",
        },

        //Key Stats
        {
          headerName: "Key Stats",
          children: [
            {
              headerName: "In Stock",
              field: "stockItems",
              width: numberColWidth,
              type: "number",
            },
            {
              headerName: "Scans",
              field: "scans",
              width: numberColWidth,
              type: "number",
            },
            {
              headerName: "Missing + Unknown",
              field: "totalMissingAndUnknown",
              width: numberColWidth + 10,
              type: "number",
            },
            {
              headerName: "Resolved",
              field: "totalMissingAndUnknownResolved",
              width: numberColWidth,
              type: "number",
            },
            {
              headerName: "% Resolved",
              field: "totalMissingAndUnknownResolvedPct",
              width: numberColWidth,
              type: "percent",
            },
          ],
        },

        //High level figures

        //Auto-Resolved
        {
          headerName: "Auto Resolutions",
          children: [
            {
              headerName: "In Stock and Scanned",
              field: "scannedInStock",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },
            {
              headerName: "% In Stock",
              field: "inStockPercent",
              width: numberColWidth,
              type: "percent",
              columnGroupShow: "open",
            },

            // {
            //   headerName: "Unknown Auto-Matched to Report",
            //   field: "unknownsAutoResolved",
            //   width: numberColWidth,
            //   type: "number",
            //   columnGroupShow: "open",
            // },
            {
              headerName: "% Auto Resolved",
              field: "totalAutoReconciledPct",
              width: groupedOpenColWidth,
              type: "percent",
            },
          ],
        },

        //Missings
        {
          headerName: "Missings",
          children: [
            {
              headerName: "Total",
              field: "missingsTotal",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },
            {
              headerName: "Auto Resolved",
              field: "missingsAutoResolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },
            {
              headerName: "Manually Resolved",
              field: "missingsManuallyResolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },

            {
              headerName: "% Resolved",
              field: "missingsResolvedPct",
              width: groupedOpenColWidth,
              type: "percent",
            },

            {
              headerName: "Unresolved",
              field: "missingsUnresolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },

            // {
            //   headerName: "% Unresolved",
            //   field: "missingsUnresolvedPct",
            //   width: groupedOpenColWidth,
            //   type: "percent",
            // },
          ],
        },

        //Unknowns
        {
          headerName: "Unknowns",
          children: [
            {
              headerName: "Total",
              field: "unknownsTotal",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },
            {
              headerName: "Auto Resolved",
              field: "unknownsAutoResolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },
            {
              headerName: "Manually Resolved",
              field: "unknownsManuallyResolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },

            {
              headerName: "% Resolved",
              field: "unknownsResolvedPct",
              width: groupedOpenColWidth,
              type: "percent",
            },

            {
              headerName: "Unresolved",
              field: "unknownsUnresolved",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
            },

            // {
            //   headerName: "% Unresolved",
            //   field: "unknownsUnresolvedPct",
            //   width: groupedOpenColWidth,
            //   type: "percent",
            // },
          ],
        },

        //Edited
        {
          headerName: "Manually Edited",
          children: [
            {
              headerName: "Regs",
              field: "regsEdited",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
              hide: this.constantsService.neverScanReg,
            },
            {
              headerName: "VINs",
              field: "vinsEdited",
              width: numberColWidth,
              type: "number",
              columnGroupShow: this.constantsService.neverScanReg
                ? null
                : "open",
            },
            {
              headerName: "Total",
              field: "totalEdited",
              width: numberColWidth,
              type: "number",
              columnGroupShow: "open",
              hide: this.constantsService.neverScanReg,
            },
            {
              headerName: "%",
              field: "totalEditedPct",
              width: groupedOpenColWidth,
              type: "percent",
            },
          ],
        },
        //Duplicates
        {
          headerName: "Duplicates",
          children: [
            {
              headerName: "Stock Items",
              field: "duplicateStockItems",
              width: numberColWidth,
              type: "number",
            },
            {
              headerName: "Scans",
              field: "duplicateScans",
              width: numberColWidth,
              type: "number",
            },
          ],
        },
      ],
      onGridReady: (event) => this.onGridReady(event),
      onColumnGroupOpened: (event) => {
        this.dashboardService.stockCheckQualityColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onColumnMoved: (event) => {
        this.dashboardService.stockCheckQualityColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onFilterChanged: (event) => {
        this.dashboardService.stockCheckQualityfilterModel =
          this.gridApi.getFilterModel();
      },
      onSortChanged: (event) => {
        this.dashboardService.stockCheckQualityColumnState =
          this.gridColumnApi?.getColumnState();
      },
      onRowDoubleClicked: (event) => {
        this.dashboardService.loadStockCheckAndGoToReconcile(
          event.data.stockCheckId
        );
      },
    };
  }

  onGridReady(event: GridReadyEvent): void {
    this.gridApi = event.api;
    this.gridColumnApi = event.columnApi;

    if (this.dashboardService.stockCheckQualityColumnState) {
      this.gridColumnApi.applyColumnState({
        state: this.dashboardService.stockCheckQualityColumnState,
        applyOrder: true,
      });
    }
    if (this.dashboardService.stockCheckQualityfilterModel) {
      this.gridApi.setFilterModel(
        this.dashboardService.stockCheckQualityfilterModel
      );
    }

    this.adjustColumnSizing();
  }

  download(): void {
    this.toastService.loadingToast("Generating Excel file...");

    const filteredRows = [];
    const columns: Column[] = this.gridColumnApi.getColumns();

    this.gridApi.forEachNodeAfterFilter((n) => {
      const columnsForExcel = {};

      columns.forEach((column) => {
        const columnId: string = column.getColId();
        columnsForExcel[columnId] = n.data[columnId];
      });

      columnsForExcel["lastUpdated"] = new Date(n.data.lastUpdated);
      columnsForExcel["stockCheckDate"] = new Date(n.data.stockCheckDate);

      filteredRows.push(columnsForExcel);
    });

    const colTypes: string[] = [];

    this.gridApi.getColumnDefs().forEach((colDef) => {
      if ((colDef as ColGroupDef).children) {
        (colDef as ColGroupDef).children.forEach((child) => {
          colTypes.push((child as ColDef).type as string);
        });
      } else {
        colTypes.push((colDef as ColDef).type as string);
      }
    });

    let sheet: SheetToExtractOld = {
      tableData: filteredRows,
      tableName: `Stock Check Quality`,
      columnWidths: this.excelExportService.workoutColWidths(filteredRows),
      colTypes: colTypes,
    };

    this.dashboardService.downloadExcelFile(sheet);
  }

  get excelIconBackgroundImage() {
    return {
      "background-image": `url(${this.logoService.provideExcelLogo()})`,
    };
  }

  adjustColumnSizing(): void {
    if (!this.gridApi || !this.gridColumnApi) return;

    // Total width of all columns
    const allColumns = this.gridColumnApi.getAllDisplayedColumns();
    const totalColumnWidth = allColumns.reduce((sum, col) => {
      return (
        sum +
          this.gridColumnApi
            .getColumnState()
            .find((c) => c.colId === col.getColId())?.width || 0
      );
    }, 0);

    // Width of the grid container
    const gridElement = document.querySelector<HTMLElement>(
      "#stockCheckQualityGrid"
    );
    const gridWidth = gridElement?.offsetWidth || 0;

    if (totalColumnWidth < gridWidth) {
      this.gridApi.sizeColumnsToFit();
    }
  }
}
